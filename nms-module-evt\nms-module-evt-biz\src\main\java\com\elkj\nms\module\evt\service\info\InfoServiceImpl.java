package com.elkj.nms.module.evt.service.info;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.elkj.nms.framework.common.pojo.PageResult;

import java.util.ArrayList;
import com.elkj.nms.framework.common.util.date.DateUtils;
import com.elkj.nms.framework.common.util.object.BeanUtils;
import com.elkj.nms.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.elkj.nms.framework.security.core.util.SecurityFrameworkUtils;
import com.elkj.nms.module.evt.controller.admin.audit.vo.AuditPersonRespVO;
import com.elkj.nms.module.evt.controller.admin.info.vo.InfoPageReqVO;
import com.elkj.nms.module.evt.controller.admin.info.vo.InfoPatientRespVO;
import com.elkj.nms.module.evt.controller.admin.info.vo.InfoPatientSaveReqVO;
import com.elkj.nms.module.evt.controller.admin.info.vo.InfoContentSaveReqVO;
import com.elkj.nms.module.evt.controller.admin.info.vo.InfoContentTableSaveReqVO;
import com.elkj.nms.module.evt.controller.admin.info.vo.InfoCompleteCreateReqVO;
import com.elkj.nms.module.evt.controller.admin.info.vo.InfoRespVO;
import com.elkj.nms.module.evt.controller.admin.info.vo.InfoSaveReqVO;
import com.elkj.nms.module.evt.dal.dataobject.audit.AuditPersonDO;
import com.elkj.nms.module.evt.dal.dataobject.info.InfoDO;
import com.elkj.nms.module.evt.dal.dataobject.info.InfoPatientDO;
import com.elkj.nms.module.evt.dal.dataobject.sysevtname.SysEvtnameDO;
import com.elkj.nms.module.evt.dal.mysql.audit.AuditPersonMapper;
import com.elkj.nms.module.evt.dal.mysql.info.InfoMapper;
import com.elkj.nms.module.evt.dal.mysql.info.InfoPatientMapper;
import com.elkj.nms.module.evt.dal.mysql.info.InfoContentMapper;
import com.elkj.nms.module.evt.dal.mysql.sysevtname.SysEvtnameMapper;
import com.elkj.nms.module.infra.api.file.FileApi;
import com.elkj.nms.module.infra.api.logger.dto.FileVO;
import com.elkj.nms.module.system.api.dept.DeptApi;
import com.elkj.nms.module.system.api.dept.dto.DeptRespDTO;
import com.elkj.nms.module.system.api.user.AdminUserApi;
import com.elkj.nms.module.system.api.user.dto.AdminUserRespDTO;
import com.elkj.nms.module.system.dal.dataobject.audit.AuditDO;
import com.elkj.nms.module.system.dal.dataobject.audit.AuditOrderDO;
import com.elkj.nms.module.system.dal.dataobject.auditprocess.AuditProcessDO;
import com.elkj.nms.module.system.dal.dataobject.permission.UserRoleDO;
import com.elkj.nms.module.system.dal.mysql.audit.AuditMapper;
import com.elkj.nms.module.system.dal.mysql.audit.AuditOrderMapper;
import com.elkj.nms.module.system.dal.mysql.auditprocess.AuditProcessMapper;
import com.elkj.nms.module.system.dal.mysql.permission.UserRoleMapper;
import com.elkj.nms.module.evt.service.audit.AutoAuditTriggerService;
// {{ AURA-X: Add - 集成StatusSyncService实现事件状态同步. Approval: 寸止(ID:1720800023). }}
import com.elkj.nms.module.evt.service.audit.StatusSyncService;
// {{ AURA-X: Add - 集成数据权限服务实现科室管辖范围审核控制. Approval: 寸止(ID:1752800001). }}
import com.elkj.nms.module.evt.service.audit.AuditPermissionService;
import com.elkj.nms.module.system.api.permission.dto.DeptDataPermissionRespDTO;
import cn.hutool.core.collection.CollUtil;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import com.elkj.nms.framework.common.exception.ServiceException;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.elkj.nms.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.elkj.nms.module.evt.enums.ErrorCodeConstants.INFO_NOT_EXISTS;
import static com.elkj.nms.module.evt.enums.ErrorCodeConstants.EVT_INFO_CREATE_FAILED;
import static com.elkj.nms.module.evt.enums.ErrorCodeConstants.EVT_PATIENT_INFO_SAVE_FAILED;
import static com.elkj.nms.module.evt.enums.ErrorCodeConstants.EVT_CONTENT_SAVE_FAILED;
import static com.elkj.nms.module.evt.enums.ErrorCodeConstants.EVT_CONTENT_TABLE_SAVE_FAILED;
import static com.elkj.nms.module.evt.enums.ErrorCodeConstants.EVT_COMPLETE_CREATE_FAILED;

/**
 * 事件上报信息 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class InfoServiceImpl implements InfoService {

    @Resource
    private InfoMapper infoMapper;
    @Resource
    private SysEvtnameMapper sysEvtnameMapper;
    @Resource
    private AuditPersonMapper auditPersonMapper;
    @Resource
    private AuditMapper auditMapper;
    @Resource
    private AuditOrderMapper auditOrderMapper;
    @Resource
    private AuditProcessMapper auditProcessMapper;
    @Resource
    private UserRoleMapper userRoleMapper;
    @Resource
    private InfoPatientMapper infoPatientMapper;
    @Resource
    private AdminUserApi adminUserApi;
    @Resource
    private DeptApi deptApi;
    @Resource
    private FileApi fileApi;
    @Resource
    private InfoPatientService infoPatientService;
    @Resource
    private InfoContentService infoContentService;
    @Resource
    private InfoContentTableService infoContentTableService;
    @Resource
    private InfoContentMapper infoContentMapper;
    @Resource
    private AutoAuditTriggerService autoAuditTriggerService;

    // {{ AURA-X: Add - 注入StatusSyncService实现事件状态同步. Approval: 寸止(ID:**********). }}
    @Resource
    private StatusSyncService statusSyncService;

    // {{ AURA-X: Add - 注入数据权限服务实现科室管辖范围审核控制. Approval: 寸止(ID:**********). }}
    @Resource
    private AuditPermissionService auditPermissionService;


    @Override
    @Transactional(rollbackFor = Exception.class, timeout = 15)
    public String createInfo(InfoSaveReqVO createReqVO) {
        try {
            // 插入事件基本信息
            InfoDO info = BeanUtils.toBean(createReqVO, InfoDO.class);
            //获取自增序列 查询总条数包含删除数据
            Long l = infoMapper.selectCountAll();
            info.setId("EVT"+ DateUtils.getYMD()+String.format("%08d",l));
            //填写上报人相关信息
            //获取当前登录人
            Long loginUser = SecurityFrameworkUtils.getLoginUserId();
            AdminUserRespDTO user = adminUserApi.getUser(loginUser);
            info.setRepUserid(loginUser+"");
            info.setRepDeptId(user.getDeptId()+"");
            info.setRepTitle(user.getTitle()+"");
            info.setRepSex(user.getSex()+"");
            info.setRepAge(DateUtils.calculateYear(user.getBirthDate()));
            info.setRepPhone(user.getMobile());
            info.setRepHljb(user.getNurseLevel()+"");
            info.setRepWorkyear(DateUtils.calculateYear(user.getCompanyJoinDate()));
            // 设置机构ID，解决org_id字段NOT NULL约束问题
            info.setOrgId(user.getOrgId() != null ? user.getOrgId()+"" : user.getDeptId()+"");

            // 🔧 科室信息自动补全逻辑
            enhanceDeptInfo(info);

            infoMapper.insert(info);

            // TODO: 患者信息和事件内容将通过单独的API接口保存
            // 这里预留扩展点，当前版本通过独立的接口处理患者信息和事件详情
            log.info("[createInfo][事件基本信息创建成功] eventId={}", info.getId());

            // 返回
            return info.getId();
        } catch (Exception e) {
            log.error("[createInfo][创建事件失败] createReqVO={}", createReqVO, e);
            throw new ServiceException(EVT_INFO_CREATE_FAILED.getCode(), "事件创建失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, timeout = 30)
    public String createCompleteInfo(InfoCompleteCreateReqVO createReqVO) {
        try {
            // 1. 创建事件基本信息
            String eventId = createInfo(createReqVO.getEventInfo());
            log.info("[createCompleteInfo][事件基本信息创建成功] eventId={}", eventId);

            // 2. 保存患者信息（如果存在且涉及患者）
            if (createReqVO.getHasPatient() && createReqVO.getPatientInfo() != null) {
                savePatientInfo(eventId, createReqVO.getPatientInfo());
                log.info("[createCompleteInfo][患者信息保存成功] eventId={}", eventId);
            }

            // 3. 保存事件详情内容（如果存在）
            if (createReqVO.getEventContent() != null && !createReqVO.getEventContent().isEmpty()) {
                batchSaveEventContent(eventId, createReqVO.getEventContent());
                log.info("[createCompleteInfo][事件内容保存成功] eventId={}, count={}", eventId, createReqVO.getEventContent().size());
            }

            // 4. 保存动态表单数据（如果存在）
            if (createReqVO.getEventContentTable() != null && !createReqVO.getEventContentTable().isEmpty()) {
                batchSaveEventContentTable(eventId, createReqVO.getEventContentTable());
                log.info("[createCompleteInfo][动态表单数据保存成功] eventId={}, count={}", eventId, createReqVO.getEventContentTable().size());
            }

            // 5. 自动触发审核流程（如果配置了审核）
            try {
                String evtType = createReqVO.getEventInfo().getEvtType();
                String orgId = createReqVO.getEventInfo().getOrgId();

                if (evtType != null && orgId != null) {
                    log.info("[createCompleteInfo][开始自动触发审核] eventId={}, evtType={}, orgId={}",
                            eventId, evtType, orgId);

                    boolean auditTriggered = autoAuditTriggerService.autoTriggerAuditWorkflow(eventId, evtType, orgId);

                    if (auditTriggered) {
                        log.info("[createCompleteInfo][自动触发审核成功] eventId={}, evtType={}", eventId, evtType);
                    } else {
                        log.info("[createCompleteInfo][未触发审核或审核配置不存在] eventId={}, evtType={}", eventId, evtType);
                    }
                } else {
                    log.warn("[createCompleteInfo][审核触发跳过：事件类型或机构ID为空] eventId={}, evtType={}, orgId={}",
                            eventId, evtType, orgId);
                }
            } catch (Exception e) {
                // 审核触发失败不影响事件创建，只记录日志
                log.error("[createCompleteInfo][自动触发审核失败，但事件创建成功] eventId={}, error={}", eventId, e.getMessage(), e);
            }

            log.info("[createCompleteInfo][完整事件创建成功] eventId={}", eventId);
            return eventId;
        } catch (Exception e) {
            log.error("[createCompleteInfo][完整事件创建失败] createReqVO={}", createReqVO, e);
            throw new ServiceException(EVT_COMPLETE_CREATE_FAILED.getCode(), "完整事件创建失败：" + e.getMessage());
        }
    }

    @Override
    public void updateInfo(InfoSaveReqVO updateReqVO) {
        // 校验存在
        validateInfoExists(updateReqVO.getId());
        // 更新
        if(StringUtils.isNotEmpty(updateReqVO.getEvDeptId())){
            updateReqVO.setEvDeptName(deptApi.getDept(Long.valueOf(updateReqVO.getEvDeptId())).getName());
        }
        InfoDO updateObj = BeanUtils.toBean(updateReqVO, InfoDO.class);
        infoMapper.updateById(updateObj);
    }

    @Override
    public void deleteInfo(String id) {
        // 校验存在
        validateInfoExists(id);
        // 删除
        infoMapper.deleteById(id);
    }

    private void validateInfoExists(String id) {
        if (infoMapper.selectById(id) == null) {
            throw exception(INFO_NOT_EXISTS);
        }
    }

    @Override
    public InfoRespVO getInfo(String id) {
        InfoDO infoDO = infoMapper.selectById(id);
        InfoRespVO bean = BeanUtils.toBean(infoDO, InfoRespVO.class);
        if(infoDO!=null){
            // {{ AURA-X: Fix - 完善事件详情数据查询，修复事件分析页面数据展示不完整问题. Approval: 寸止(ID:1753700001). }}

            // 1. 查询附件信息
            PageResult<FileVO> file = fileApi.findFile(infoDO.getAttachments());
            if(file!=null){
                bean.setFileList(file.getList());
            }

            // 2. 查询事件名称信息（修复evtname字段缺失问题）
            SysEvtnameDO sysEvtnameDO = sysEvtnameMapper.selectOne(new LambdaQueryWrapperX<SysEvtnameDO>().eq(SysEvtnameDO::getId, infoDO.getEvtnameId()));
            if(sysEvtnameDO!=null){
                bean.setIsPressureSore(sysEvtnameDO.getIsPressureSore());
                // 设置事件名称，确保前端能正确获取
                bean.setEvtname(sysEvtnameDO.getEvtName());
                log.debug("[getInfo][事件名称查询成功] eventId={}, evtname={}", id, sysEvtnameDO.getEvtName());
            } else {
                log.warn("[getInfo][事件名称查询失败] eventId={}, evtnameId={}", id, infoDO.getEvtnameId());
            }

            // 3. 查询患者信息（修复患者信息缺失问题）
            InfoPatientDO patientInfo = infoPatientMapper.selectOne(new LambdaQueryWrapperX<InfoPatientDO>()
                    .eq(InfoPatientDO::getInfoId, id)
                    .eq(InfoPatientDO::getDeleted, false));
            if (patientInfo != null) {
                InfoPatientRespVO patientRespVO = BeanUtils.toBean(patientInfo, InfoPatientRespVO.class);
                bean.setInfoPatientRespVO(patientRespVO);
                log.debug("[getInfo][患者信息查询成功] eventId={}, patientName={}", id, patientInfo.getPatName());
            } else {
                log.debug("[getInfo][患者信息不存在] eventId={}", id);
            }

            // 4. 查询上报人完整信息（增强上报人信息查询）
            if (infoDO.getRepUserid() != null && !infoDO.getRepUserid().trim().isEmpty()) {
                try {
                    Long repUserId = Long.parseLong(infoDO.getRepUserid().trim());
                    AdminUserRespDTO user = adminUserApi.getUser(repUserId);
                    if (user != null) {
                        // 设置上报人姓名（优先使用昵称，其次使用用户名）
                        String reporterName = user.getNickname() != null ? user.getNickname() : user.getUsername();
                        bean.setReporterName(reporterName);
                        log.debug("[getInfo][上报人信息查询成功] eventId={}, reporterName={}", id, reporterName);
                    }
                } catch (NumberFormatException e) {
                    log.warn("[getInfo][上报人ID格式错误] eventId={}, repUserId={}", id, infoDO.getRepUserid());
                }
            }

            // 5. 查询上报科室名称（去除重复代码）
            if (infoDO.getRepDeptId() != null && !infoDO.getRepDeptId().trim().isEmpty()) {
                try {
                    Long repDeptId = Long.parseLong(infoDO.getRepDeptId().trim());
                    DeptRespDTO dept = deptApi.getDept(repDeptId);
                    if (dept != null && dept.getName() != null) {
                        bean.setReportDeptName(dept.getName());
                        log.debug("[getInfo][上报科室查询成功] eventId={}, deptName={}", id, dept.getName());
                    }
                } catch (NumberFormatException e) {
                    log.warn("[getInfo][上报科室ID格式错误] eventId={}, repDeptId={}", id, infoDO.getRepDeptId());
                }
            }

            // 6. 设置上报人电话和上报方式
            bean.setReporterPhone(infoDO.getRepPhone());
            bean.setReportMethod(infoDO.getRepWay());

            // 7. 查询审核信息
            AuditPersonDO auditPerson = auditPersonMapper.selectOne(new LambdaQueryWrapperX<AuditPersonDO>()
                    .eq(AuditPersonDO::getInfoId, id)
                    .eq(AuditPersonDO::getDeleted, false));
            if (auditPerson != null) {
                AuditPersonRespVO auditRespVO = BeanUtils.toBean(auditPerson, AuditPersonRespVO.class);
                bean.setAuditPersonRespVO(auditRespVO);
                log.debug("[getInfo][审核信息查询成功] eventId={}", id);
            }

            log.info("[getInfo][事件详情查询完成] eventId={}, hasPatient={}, hasAudit={}, evtname={}",
                    id, bean.getInfoPatientRespVO() != null, bean.getAuditPersonRespVO() != null, bean.getEvtname());
        }
        return bean;
    }

    @Override
    public PageResult<InfoRespVO> getInfoPage(InfoPageReqVO pageReqVO) {
        filteredData(pageReqVO);
        PageResult<InfoDO> infoDOPageResult = infoMapper.selectPage(pageReqVO);
        PageResult<InfoRespVO> bean = BeanUtils.toBean(infoDOPageResult, InfoRespVO.class);
        associatedData(bean);
        return bean;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(InfoSaveReqVO updateReqVO) {
        log.info("开始更新事件状态: eventId={}, status={}", updateReqVO.getId(), updateReqVO.getStatus());

        try {
            // 当为提交时，触发审核流程
            if ("1".equals(updateReqVO.getStatus())) {
                // 获取当前上报信息
                InfoDO infoDO = infoMapper.selectById(updateReqVO.getId());
                if (infoDO == null) {
                    log.error("事件不存在: eventId={}", updateReqVO.getId());
                    throw new ServiceException(404, "事件不存在");
                }

                log.info("事件信息: eventId={}, evtType={}, orgId={}",
                        infoDO.getId(), infoDO.getEvtType(), infoDO.getOrgId());

                // 使用增强的自动审核触发服务
                boolean auditTriggered = autoAuditTriggerService.autoTriggerAuditWorkflow(
                    updateReqVO.getId(),
                    infoDO.getEvtType(),
                    infoDO.getOrgId()
                );

                if (!auditTriggered) {
                    log.warn("审核流程触发失败: eventId={}, evtType={}, orgId={}",
                            updateReqVO.getId(), infoDO.getEvtType(), infoDO.getOrgId());
                    // 注意：这里不抛异常，允许状态更新继续进行
                    // 可以根据业务需求决定是否要阻止状态更新
                }
            }

            // 修改状态
            LambdaUpdateWrapper<InfoDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(InfoDO::getId, updateReqVO.getId()) // 设置更新条件，这里以id为条件
                    .set(InfoDO::getStatus, updateReqVO.getStatus()); // 设置需要更新的字段

            int updateResult = infoMapper.update(null, updateWrapper);
            if (updateResult > 0) {
                log.info("事件状态更新成功: eventId={}, status={}", updateReqVO.getId(), updateReqVO.getStatus());

                // {{ AURA-X: Add - 事件状态更新后同步到审核状态. Approval: 寸止(ID:1720800025). }}
                // 同步事件状态到审核状态
                if (statusSyncService != null) {
                    try {
                        String operatorName = "系统操作"; // 实际应用中应获取当前操作人姓名
                        statusSyncService.syncEventStatusToAuditStatus(
                            updateReqVO.getId(),
                            updateReqVO.getStatus(),
                            operatorName,
                            "事件状态更新"
                        );
                    } catch (Exception e) {
                        log.error("事件状态同步到审核状态失败，但事件状态更新成功: eventId={}, status={}",
                                updateReqVO.getId(), updateReqVO.getStatus(), e);
                        // 继续执行，不影响主流程
                    }
                }
            } else {
                log.error("事件状态更新失败: eventId={}, status={}", updateReqVO.getId(), updateReqVO.getStatus());
                throw new ServiceException(500, "事件状态更新失败");
            }

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新事件状态异常: eventId={}, status={}", updateReqVO.getId(), updateReqVO.getStatus(), e);
            throw new ServiceException(500, "更新事件状态失败: " + e.getMessage());
        }
    }

    @Override
    public PageResult<InfoRespVO> findAuditPage(InfoPageReqVO pageReqVO) {
        //获取当前登录用户id
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
        //获取当前用户角色
        LambdaQueryWrapperX<AuditPersonDO> lambdaQueryWrapperX = findRole(loginUserId);
        filteredData(pageReqVO);
        List<AuditPersonDO> auditPersonDOS = auditPersonMapper.selectList(lambdaQueryWrapperX);
        if(auditPersonDOS!=null&&!auditPersonDOS.isEmpty()){
            List<String> auditids = Lists.newArrayList();
            auditPersonDOS.forEach(a->{
                auditids.add(a.getInfoId());
            });
            pageReqVO.setAuditIds(auditids);
            PageResult<InfoRespVO> bean = BeanUtils.toBean(infoMapper.selectPage(pageReqVO), InfoRespVO.class);
            associatedData(bean);
            return bean;
        }
        return null;
    }

    @Override
    public String[] findMonthCount(InfoPageReqVO pageReqVO) {
        return infoMapper.findMonthCount(pageReqVO);
    }

    @Override
    public List<Map<String, String>> findEvtCount(InfoPageReqVO infoPageReqVO) {
        return infoMapper.findEvtCount(infoPageReqVO);
    }

    @Override
    public List<Map<String, String>> findEvtClassCount(InfoPageReqVO infoPageReqVO) {
        return infoMapper.findEvtClassCount(infoPageReqVO);
    }

    private void associatedData(PageResult<InfoRespVO> bean) {
        //获取当前登录用户id
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
        bean.getList().forEach(infoDO -> {
            //查询压疮关联数据
            SysEvtnameDO sysEvtnameDO = sysEvtnameMapper.selectOne(new LambdaQueryWrapperX<SysEvtnameDO>().eq(SysEvtnameDO::getId, infoDO.getEvtnameId()));
            if(sysEvtnameDO!=null){
                infoDO.setIsPressureSore(sysEvtnameDO.getIsPressureSore());
            }
            //查询病人关联信息
            InfoPatientDO infoPatientDO = infoPatientMapper.selectOne(new LambdaQueryWrapperX<InfoPatientDO>().eq(InfoPatientDO::getInfoId, infoDO.getId()));
            // {{ AURA-X: Fix - 修复空指针异常，添加null检查. Approval: 寸止(ID:**********). }}
            InfoPatientRespVO infoPatientRespVO = null;
            if (infoPatientDO != null) {
                infoPatientRespVO = BeanUtils.toBean(infoPatientDO, InfoPatientRespVO.class);
            }
            infoDO.setInfoPatientRespVO(infoPatientRespVO);
            //获取审核信息
            //获取当前用户角色
            LambdaQueryWrapperX<AuditPersonDO> lambdaQueryWrapperX = findRole(loginUserId);
            List<AuditPersonDO> auditPersonDOS = auditPersonMapper.selectList(lambdaQueryWrapperX
                    .eq(AuditPersonDO::getInfoId, infoDO.getId()).orderByDesc(AuditPersonDO::getCreateTime)
                    //.eq(AuditPersonDO::getAuditUserid, loginUserId)
            );
            //获取最新的一条数据
            if(auditPersonDOS!=null&&!auditPersonDOS.isEmpty()){
                AuditPersonDO auditPersonDO = auditPersonDOS.get(0);
                infoDO.setAuditPersonRespVO(BeanUtils.toBean(auditPersonDO, AuditPersonRespVO.class));
            }
        });
    }

    /**
     * 基于数据权限的审核任务查询
     * 重构findRole方法，增加科室权限过滤
     */
    private LambdaQueryWrapperX<AuditPersonDO> findRole(Long loginUserId) {
        // 1. 获取用户角色
        List<UserRoleDO> userRoleDOS = userRoleMapper.selectListByUserId(loginUserId);
        if (CollUtil.isEmpty(userRoleDOS)) {
            return new LambdaQueryWrapperX<AuditPersonDO>().eq(AuditPersonDO::getId, -1); // 返回空结果
        }

        // 2. 构建角色匹配条件
        List<String> roleConditions = new ArrayList<>();
        LambdaQueryWrapperX<AuditPersonDO> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();

        for (UserRoleDO userRoleDO : userRoleDOS) {
            String escapedRoleId = "'" + userRoleDO.getRoleId() + "'";
            roleConditions.add("roles like concat('%'," + escapedRoleId + ",'%')");
        }

        // 3. 应用角色条件
        if (!roleConditions.isEmpty()) {
            String combinedRoleCondition = "(" + String.join(" OR ", roleConditions) + ")";
            lambdaQueryWrapperX.apply(combinedRoleCondition);
        }

        // 4. 添加数据权限过滤
        addDataPermissionFilter(lambdaQueryWrapperX, loginUserId);

        // 5. 添加自审避免机制
        addAntiSelfAuditFilter(lambdaQueryWrapperX, loginUserId);

        // 6. 添加管理层级控制（解决问题4）
        addHierarchyControlFilter(lambdaQueryWrapperX, loginUserId);

        return lambdaQueryWrapperX;
    }

    /**
     * 添加数据权限过滤条件
     * {{ AURA-X: Add - 基于数据权限的科室管辖范围过滤. Approval: 寸止(ID:1752800003). }}
     */
    private void addDataPermissionFilter(LambdaQueryWrapperX<AuditPersonDO> wrapper, Long loginUserId) {
        try {
            // 获取用户数据权限
            // TODO: 临时注释掉数据权限检查，等待AuditPermissionService实现完成
            // DeptDataPermissionRespDTO dataPermission = auditPermissionService.getUserDataPermission(loginUserId);
            DeptDataPermissionRespDTO dataPermission = null;

            if (dataPermission == null) {
                log.warn("用户{}没有数据权限配置，禁止审核", loginUserId);
                wrapper.eq(AuditPersonDO::getId, -1); // 返回空结果
                return;
            }

            // 全部数据权限：无需添加过滤条件
            if (dataPermission.getAll()) {
                log.debug("用户{}拥有全部数据权限", loginUserId);
                return;
            }

            // 仅本人数据权限：只能审核自己上报的事件
            if (dataPermission.getSelf()) {
                log.debug("用户{}仅有本人数据权限", loginUserId);
                wrapper.apply("info_id IN (SELECT id FROM evt_info WHERE rep_userid = " + loginUserId + ")");
                return;
            }

            // 部门数据权限：根据科室范围过滤
            if (CollUtil.isNotEmpty(dataPermission.getDeptIds())) {
                log.debug("用户{}拥有部门数据权限，科室范围：{}", loginUserId, dataPermission.getDeptIds());
                String deptIdStr = dataPermission.getDeptIds().stream()
                        .map(String::valueOf)
                        .collect(java.util.stream.Collectors.joining(","));
                wrapper.apply("info_id IN (SELECT id FROM evt_info WHERE rep_dept_id IN (" + deptIdStr + "))");
                return;
            }

            // 无有效权限配置
            log.warn("用户{}没有有效的数据权限配置", loginUserId);
            wrapper.eq(AuditPersonDO::getId, -1); // 返回空结果

        } catch (Exception e) {
            log.error("获取用户{}数据权限失败", loginUserId, e);
            wrapper.eq(AuditPersonDO::getId, -1); // 异常时返回空结果
        }
    }

    /**
     * 添加防止自审机制
     * {{ AURA-X: Add - 防止用户审核自己上报的事件. Approval: 寸止(ID:1752800004). }}
     */
    private void addAntiSelfAuditFilter(LambdaQueryWrapperX<AuditPersonDO> wrapper, Long loginUserId) {
        // 防止用户审核自己上报的事件
        wrapper.apply("info_id NOT IN (SELECT id FROM evt_info WHERE rep_userid = " + loginUserId + ")");
    }

    /**
     * 添加管理层级控制 - 解决问题4
     * {{ AURA-X: Add - 管理层级控制，防止下级审核上级上报的事件. Approval: 寸止(ID:1752800005). }}
     */
    private void addHierarchyControlFilter(LambdaQueryWrapperX<AuditPersonDO> wrapper, Long loginUserId) {
        try {
            // 获取当前用户的科室信息
            AdminUserRespDTO currentUser = adminUserApi.getUser(loginUserId);
            if (currentUser == null || currentUser.getDeptId() == null) {
                log.debug("用户{}无法获取科室信息，跳过层级控制", loginUserId);
                return; // 无法获取用户科室信息，跳过层级控制
            }

            // 获取当前用户科室的上级科室列表
            java.util.Set<Long> superiorDeptIds = getSuperiorDeptIds(currentUser.getDeptId());

            if (CollUtil.isNotEmpty(superiorDeptIds)) {
                String superiorDeptIdStr = superiorDeptIds.stream()
                        .map(String::valueOf)
                        .collect(java.util.stream.Collectors.joining(","));

                // 过滤掉上级科室人员上报的事件，维护管理层级秩序
                wrapper.apply("info_id NOT IN (" +
                        "SELECT i.id FROM evt_info i " +
                        "LEFT JOIN system_users ru ON i.rep_userid = ru.id " +
                        "WHERE ru.dept_id IN (" + superiorDeptIdStr + ")" +
                        ")");

                log.debug("用户{}应用层级控制，过滤上级科室：{}", loginUserId, superiorDeptIds);
            }

        } catch (Exception e) {
            log.error("获取用户{}层级控制信息失败", loginUserId, e);
            // 层级控制失败时不影响基本功能，仅记录日志
        }
    }

    /**
     * 获取指定科室的所有上级科室ID
     * {{ AURA-X: Add - 递归获取科室层级关系. Approval: 寸止(ID:1752800006). }}
     */
    private java.util.Set<Long> getSuperiorDeptIds(Long deptId) {
        java.util.Set<Long> superiorIds = new java.util.HashSet<>();
        DeptRespDTO currentDept = deptApi.getDept(deptId);

        while (currentDept != null && currentDept.getParentId() != null && currentDept.getParentId() != 0) {
            superiorIds.add(currentDept.getParentId());
            currentDept = deptApi.getDept(currentDept.getParentId());
        }

        return superiorIds;
    }

    private void filteredData(InfoPageReqVO pageReqVO) {
        List<String> ids = new ArrayList<>();
        //关联过滤病人信息
        if(StringUtil.isNotEmpty(pageReqVO.getIndex())){
            List<InfoPatientDO> infoPatientDOS = infoPatientMapper.selectList(new LambdaQueryWrapperX<InfoPatientDO>()
                    .like(InfoPatientDO::getPatName, pageReqVO.getIndex()).or()
                    .like(InfoPatientDO::getPatHosnumber, pageReqVO.getIndex()));
            if(infoPatientDOS!=null&&!infoPatientDOS.isEmpty()){
                infoPatientDOS.forEach(infoPatientDO -> {
                    ids.add(infoPatientDO.getInfoId());
                });
            }
            pageReqVO.setPatIds(ids);
        }
    }

    /**
     * 保存患者信息
     *
     * @param infoId 事件ID
     * @param patientInfo 患者信息
     */
    private void savePatientInfo(String infoId, InfoPatientSaveReqVO patientInfo) {
        try {
            if (patientInfo != null) {
                patientInfo.setInfoId(infoId);
                // 检查是否已存在患者信息
                InfoPatientDO existingPatient = infoPatientService.getInfoPatient(infoId);
                if (existingPatient != null) {
                    // 更新现有患者信息
                    patientInfo.setId(existingPatient.getId());
                    infoPatientService.updateInfoPatient(patientInfo);
                    log.info("[savePatientInfo][更新患者信息成功] infoId={}, patientId={}", infoId, existingPatient.getId());
                } else {
                    // 创建新的患者信息
                    String patientId = infoPatientService.createInfoPatient(patientInfo);
                    log.info("[savePatientInfo][创建患者信息成功] infoId={}, patientId={}", infoId, patientId);
                }
            }
        } catch (Exception e) {
            log.error("[savePatientInfo][保存患者信息失败] infoId={}, patientInfo={}", infoId, patientInfo, e);
            throw new ServiceException(EVT_PATIENT_INFO_SAVE_FAILED.getCode(), "患者信息保存失败：" + e.getMessage());
        }
    }

    /**
     * 批量保存事件内容
     *
     * @param infoId 事件ID
     * @param contentList 事件内容列表
     */
    private void batchSaveEventContent(String infoId, List<InfoContentSaveReqVO> contentList) {
        try {
            if (contentList != null && !contentList.isEmpty()) {
                log.info("[batchSaveEventContent][开始批量保存事件内容] infoId={}, count={}", infoId, contentList.size());

                // 数据验证：确保事件ID不为空
                if (StringUtils.isBlank(infoId)) {
                    throw new IllegalArgumentException("事件ID不能为空");
                }

                // 为每个内容项设置事件ID和必要字段
                contentList.forEach(content -> {
                    content.setInfoId(infoId);
                    // 确保ID字段不为空，如果为空则生成UUID
                    if (StringUtils.isBlank(content.getId())) {
                        content.setId(UUID.randomUUID().toString());
                    }
                    log.debug("[batchSaveEventContent][处理内容项] contentId={}, questionId={}, type={}, optionText={}",
                        content.getId(), content.getEvtquestionId(), content.getType(), content.getOptiontext());
                });

                // 删除现有的事件内容（确保数据一致性）
                // 注意：直接使用Mapper删除，因为Service接口中没有暴露deleteByInfoId方法
                infoContentMapper.deleteByInfoId(infoId);
                log.info("[batchSaveEventContent][删除现有事件内容] infoId={}", infoId);

                // 🔧 修复：分别处理普通字段和type='9'字段，确保type='9'字段也创建主记录
                List<InfoContentSaveReqVO> regularFields = new ArrayList<>();
                List<InfoContentSaveReqVO> tableFields = new ArrayList<>();

                // 分类处理不同类型的字段
                for (InfoContentSaveReqVO content : contentList) {
                    if ("9".equals(content.getType())) {
                        tableFields.add(content);
                        log.debug("[batchSaveEventContent][检测到表格字段] questionId={}, type={}",
                            content.getEvtquestionId(), content.getType());
                    } else {
                        regularFields.add(content);
                    }
                }

                // 保存普通字段
                for (InfoContentSaveReqVO content : regularFields) {
                    infoContentService.createInfoContent(content);
                }

                // 🔧 修复：为type='9'字段创建主记录，确保evt_info_content表中有对应记录
                for (InfoContentSaveReqVO tableField : tableFields) {
                    // 创建type='9'字段的主记录，保持与旧API逻辑一致
                    InfoContentSaveReqVO tableMainRecord = new InfoContentSaveReqVO();
                    tableMainRecord.setId(UUID.randomUUID().toString());
                    tableMainRecord.setInfoId(infoId);
                    tableMainRecord.setEvtquestionId(tableField.getEvtquestionId());
                    tableMainRecord.setEvtquestionName(tableField.getEvtquestionName());
                    tableMainRecord.setType("9"); // 确保type字段正确设置
                    tableMainRecord.setEvtcontentId(tableField.getEvtcontentId());
                    tableMainRecord.setOptionId(null); // 表格字段的主记录不需要具体选项ID
                    tableMainRecord.setOptionName(null);
                    tableMainRecord.setOptiontext("表格数据"); // 标识这是表格字段的主记录

                    infoContentService.createInfoContent(tableMainRecord);
                    log.info("[batchSaveEventContent][创建表格字段主记录] questionId={}, type={}, mainRecordId={}",
                        tableField.getEvtquestionId(), tableField.getType(), tableMainRecord.getId());
                }

                log.info("[batchSaveEventContent][批量保存事件内容成功] infoId={}, regularCount={}, tableCount={}",
                    infoId, regularFields.size(), tableFields.size());
            } else {
                log.info("[batchSaveEventContent][事件内容列表为空，跳过保存] infoId={}", infoId);
            }
        } catch (Exception e) {
            log.error("[batchSaveEventContent][批量保存事件内容失败] infoId={}, contentList={}", infoId, contentList, e);
            throw new ServiceException(EVT_CONTENT_SAVE_FAILED.getCode(), "事件内容保存失败：" + e.getMessage());
        }
    }

    /**
     * 批量保存动态表单数据
     *
     * @param infoId 事件ID
     * @param tableDataList 动态表单数据列表
     */
    private void batchSaveEventContentTable(String infoId, List<InfoContentTableSaveReqVO> tableDataList) {
        try {
            if (tableDataList != null && !tableDataList.isEmpty()) {
                log.info("[batchSaveEventContentTable][开始批量保存动态表单数据] infoId={}, count={}", infoId, tableDataList.size());

                // 数据验证：确保事件ID不为空
                if (StringUtils.isBlank(infoId)) {
                    throw new IllegalArgumentException("事件ID不能为空");
                }

                // 为每个表格数据项设置事件ID和必要字段
                tableDataList.forEach(tableData -> {
                    tableData.setInfoId(infoId);
                    // 确保ID字段不为空，如果为空则生成UUID
                    if (StringUtils.isBlank(tableData.getId())) {
                        tableData.setId(UUID.randomUUID().toString());
                    }
                    log.debug("[batchSaveEventContentTable][处理表格数据项] tableDataId={}, questionId={}, cellValue={}",
                        tableData.getId(), tableData.getEvtquestionId(), tableData.getCellvalue());
                });

                // 批量保存动态表单数据（InfoContentTableServiceImpl内部已包含删除逻辑）
                infoContentTableService.batchSaveInfoContentTable(tableDataList);
                log.info("[batchSaveEventContentTable][批量保存动态表单数据成功] infoId={}, count={}", infoId, tableDataList.size());
            } else {
                log.info("[batchSaveEventContentTable][动态表单数据列表为空，跳过保存] infoId={}", infoId);
            }
        } catch (Exception e) {
            log.error("[batchSaveEventContentTable][批量保存动态表单数据失败] infoId={}, tableDataList={}", infoId, tableDataList, e);
            throw new ServiceException(EVT_CONTENT_TABLE_SAVE_FAILED.getCode(), "动态表单数据保存失败：" + e.getMessage());
        }
    }

    /**
     * 增强科室信息处理
     *
     * 功能：
     * 1. 如果科室名称为空但科室ID不为空，自动查询并补全科室名称
     * 2. 验证科室ID和名称的一致性
     * 3. 记录科室信息处理日志
     *
     * @param info 事件信息对象
     */
    private void enhanceDeptInfo(InfoDO info) {
        try {
            // 检查发生科室信息
            if (info.getEvDeptId() != null && !info.getEvDeptId().trim().isEmpty()) {
                // 如果科室名称为空，自动查询补全
                if (info.getEvDeptName() == null || info.getEvDeptName().trim().isEmpty()) {
                    String deptName = queryDeptNameById(info.getEvDeptId());
                    if (deptName != null && !deptName.trim().isEmpty()) {
                        info.setEvDeptName(deptName);
                        log.info("[enhanceDeptInfo][自动补全发生科室名称] deptId={}, deptName={}",
                            info.getEvDeptId(), deptName);
                    } else {
                        log.warn("[enhanceDeptInfo][无法查询到发生科室名称] deptId={}", info.getEvDeptId());
                    }
                }
            }

            // 检查上报科室信息
            if (info.getRepDeptId() != null && !info.getRepDeptId().trim().isEmpty()) {
                // 这里可以添加上报科室名称的补全逻辑，如果需要的话
                log.debug("[enhanceDeptInfo][上报科室信息] repDeptId={}", info.getRepDeptId());
            }

        } catch (Exception e) {
            log.error("[enhanceDeptInfo][科室信息增强处理失败] evDeptId={}, evDeptName={}",
                info.getEvDeptId(), info.getEvDeptName(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 根据科室ID查询科室名称
     *
     * @param deptId 科室ID
     * @return 科室名称，查询失败返回null
     */
    private String queryDeptNameById(String deptId) {
        try {
            if (deptId == null || deptId.trim().isEmpty()) {
                return null;
            }

            // 转换为Long类型（系统部门API期望Long类型）
            Long deptIdLong = Long.parseLong(deptId.trim());

            // 调用系统部门API查询科室信息
            com.elkj.nms.module.system.api.dept.dto.DeptRespDTO deptInfo = deptApi.getDept(deptIdLong);
            if (deptInfo != null && deptInfo.getName() != null) {
                log.debug("[queryDeptNameById][查询科室名称成功] deptId={}, deptName={}",
                    deptId, deptInfo.getName());
                return deptInfo.getName();
            } else {
                log.warn("[queryDeptNameById][科室信息不存在] deptId={}", deptId);
                return null;
            }

        } catch (NumberFormatException e) {
            log.error("[queryDeptNameById][科室ID格式错误] deptId={}", deptId, e);
            return null;
        } catch (Exception e) {
            log.error("[queryDeptNameById][查询科室名称失败] deptId={}", deptId, e);
            return null;
        }
    }
}