<!--
  事件分析管理页面 - 渐进式增强版本

  <AUTHOR>
  @version 1.5.0
  @since 2025-02-14
-->

<template>
  <div class="event-analyze-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>事件分析详情</h1>
      <div class="header-actions">
        <a-space>
          <a-button type="primary" @click="handleOpenAnalysisTools">
            <template #icon><PlusOutlined /></template>
            分析工具
          </a-button>
          <a-button @click="handleRefresh" :loading="isRefreshing">
            <ReloadOutlined />
            刷新
          </a-button>
          <a-button @click="handleBack">
            <ArrowLeftOutlined />
            返回
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isInitializing" class="loading-container">
      <a-spin size="large" tip="正在加载事件分析数据...">
        <div class="loading-placeholder"></div>
      </a-spin>
    </div>

    <!-- 主要内容区域 -->
    <div v-else class="main-content">
      <!-- 左侧面板 -->
      <div class="left-panel">
        <!-- 事件基础信息 -->
        <a-card title="事件基础信息" class="event-info-card" :loading="isLoadingEvent">
          <template #extra>
            <a-button
              type="link"
              size="small"
              @click="toggleDetailedInfo"
              :loading="isDetailLoading"
            >
              <template #icon>
                <DownOutlined v-if="!showDetailedInfo" />
                <UpOutlined v-else />
              </template>
              {{ showDetailedInfo ? '收起详细信息' : '查看详细信息' }}
            </a-button>
          </template>

          <div v-if="currentEvent" class="event-info-content">
            <a-descriptions :column="1" size="small">
              <a-descriptions-item label="事件编号">{{ currentEvent.eventCode }}</a-descriptions-item>
              <a-descriptions-item label="事件名称">{{ currentEvent.eventName }}</a-descriptions-item>
              <a-descriptions-item label="事件类型">{{ currentEvent.eventType }}</a-descriptions-item>
              <a-descriptions-item label="严重程度">
                <a-tag :color="getSeverityColor(currentEvent.severity)">
                  {{ getSeverityText(currentEvent.severity) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="上报人">{{ currentEvent.reporterName }}</a-descriptions-item>
              <a-descriptions-item label="所属部门">{{ currentEvent.deptName }}</a-descriptions-item>
            </a-descriptions>
            <div v-if="currentEvent.description" class="event-description">
              <h4>事件描述：</h4>
              <p>{{ currentEvent.description }}</p>
            </div>

            <!-- 详细信息展示区域 - 使用新的统一组件 -->
            <div v-if="showDetailedInfo" class="detailed-info-section">
              <a-divider orientation="left" orientation-margin="0">
                <span style="font-size: 14px; color: #1890ff;">详细信息</span>
              </a-divider>

              <!-- 统一事件信息展示组件 -->
              <div class="unified-event-info">
                <EventInfoDisplay
                  :event-id="eventId"
                  :display-mode="'analysis'"
                  :data-source="'analysis'"
                  :show-section-titles="true"
                  :bordered="true"
                  :size="'middle'"
                  :auto-load="true"
                  :clickable-fields="['eventId', 'patientName', 'reporterName']"
                  :sensitive-fields="['patientName', 'hospitalNumber', 'phone']"
                  :field-labels="eventFieldLabels"
                  :field-formatters="eventFieldFormatters"
                  @data-loaded="handleEventDataLoaded"
                  @field-click="handleEventFieldClick"
                  @section-toggle="handleEventSectionToggle"
                  @error="handleEventDataError"
                  @config-change="handleEventConfigChange"
                  @description-change="handleEventDescriptionChange"
                  @attachment-click="handleEventAttachmentClick"
                />

                <!-- 备用简化版本（调试用） -->
                <div v-if="showDebugInfo" style="margin-top: 16px;">
                  <a-card title="调试信息" size="small" :bordered="true">
                    <p>事件ID: {{ eventId }}</p>
                    <p>显示模式: analysis</p>
                    <p>数据源: analysis</p>
                    <p>组件状态: 已启用完整统一组件</p>
                    <a-button size="small" @click="testEventInfoComponent">测试组件功能</a-button>
                  </a-card>
                </div>
              </div>

              <!-- 保留原有组件作为备用 -->
              <div v-if="showLegacyInfo" style="margin-top: 24px;">
                <a-divider orientation="left" orientation-margin="0">
                  <span style="font-size: 14px; color: #8c8c8c;">原有详细信息（备用）</span>
                </a-divider>
                <!-- EventDetailedInfo 暂时注释掉避免导入问题 -->
                <!-- <EventDetailedInfo
                  :event-detail-data="eventDetailData"
                  :loading="detailLoading"
                  :errors="detailErrors"
                  :show-navigator="false"
                  @retry="handleRetryDetailData"
                  @file-preview="handleFilePreview"
                  @file-download="handleFileDownload"
                  @file-reference="handleFileReference"
                /> -->
                <div style="padding: 20px; text-align: center; color: #999;">
                  EventDetailedInfo 组件已暂时禁用
                </div>
              </div>
            </div>
          </div>
          <a-empty v-else description="暂无事件信息" />
        </a-card>

        <!-- 分析任务列表 -->
        <a-card title="分析任务" class="analysis-tasks-card" :loading="isLoadingTasks">
          <template #extra>
            <a-button type="primary" size="small" @click="handleTaskCreate">
              <PlusOutlined />
              新建
            </a-button>
          </template>

          <div v-if="analysisTasks.length > 0" class="task-list">
            <div
              v-for="task in analysisTasks"
              :key="task.id"
              class="task-item"
              :class="{ 'active': selectedTaskId === task.id }"
              @click="handleTaskSelect(task)"
            >
              <div class="task-header">
                <span class="task-title">{{ task.title }}</span>
                <a-tag :color="getStatusColor(task.status)" size="small">
                  {{ getStatusText(task.status) }}
                </a-tag>
              </div>
              <div class="task-info">
                <div class="info-item">
                  <span class="label">分析人：</span>
                  <span class="value">{{ task.assignee }}</span>
                </div>
                <div class="info-item">
                  <span class="label">优先级：</span>
                  <a-tag :color="getPriorityColor(task.priority)" size="small">
                    {{ getPriorityText(task.priority) }}
                  </a-tag>
                </div>
              </div>
              <div v-if="task.progress !== undefined" class="task-progress">
                <a-progress :percent="task.progress" size="small" />
              </div>
            </div>
          </div>
          <a-empty v-else description="暂无分析任务" />
        </a-card>
      </div>

      <!-- 中间面板 - 分析工作区 -->
      <div class="center-panel">
        <!-- AnalysisWorkspace 暂时注释掉避免导入问题 -->
        <!-- <AnalysisWorkspace
          :initial-data="analysisData"
          :readonly="isViewMode"
          :loading="isLoadingAnalysis"
          :event-id="eventId"
          :analysis-id="selectedTaskId"
          @data-change="handleAnalysisDataChange"
          @save="handleSave"
          @submit="handleSubmit"
        /> -->
        <div style="padding: 20px; text-align: center; color: #999;">
          AnalysisWorkspace 组件已暂时禁用
        </div>
      </div>

      <!-- 右侧面板 -->
      <div class="right-panel">
        <!-- 知识库 -->
        <a-card title="知识库" class="knowledge-card" :loading="isLoadingKnowledge">
          <div class="knowledge-content">
            <a-input-search
              placeholder="搜索相关知识..."
              style="margin-bottom: 12px;"
            />
            <a-list size="small">
              <a-list-item>
                <a-list-item-meta
                  title="跌倒事件分析指南"
                  description="患者跌倒事件的标准分析流程"
                />
              </a-list-item>
              <a-list-item>
                <a-list-item-meta
                  title="根因分析方法"
                  description="5Why分析法的应用指导"
                />
              </a-list-item>
              <a-list-item>
                <a-list-item-meta
                  title="改进措施模板"
                  description="常用的改进措施参考模板"
                />
              </a-list-item>
            </a-list>
          </div>
        </a-card>

        <!-- 协作讨论 -->
        <a-card title="协作讨论" class="collaboration-card">
          <div class="collaboration-content">
            <div class="message-list">
              <div class="message-item">
                <div class="message-header">
                  <span class="sender">张医生</span>
                  <span class="time">10分钟前</span>
                </div>
                <div class="message-content">
                  这个事件需要重点关注环境因素
                </div>
              </div>
            </div>
            <a-input-search
              placeholder="输入讨论内容..."
              enter-button="发送"
              style="margin-top: 12px;"
            />
          </div>
        </a-card>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  ReloadOutlined,
  ArrowLeftOutlined,
  PlusOutlined,
  SaveOutlined,
  CheckOutlined,
  DownOutlined,
  UpOutlined
} from '@ant-design/icons-vue'
import { formatDateTime } from '@/utils/dateUtils'

// 导入组件 - 使用动态导入避免循环依赖
// import AnalysisWorkspace from './components/AnalysisWorkspace.vue'
// import EventDetailedInfo from './components/EventDetailedInfo.vue'
import { EventInfoDisplay } from '@/components/EventInfo'

// 导入API
// import { getAnalyzeTaskDetail, getEventInfo, getAnalysisDetails } from '@/api/evt/analyze'

// 导入Composables
// import { useEventDetailData } from './composables/useEventDetailData'

// 路由和基础状态
const route = useRoute()
const router = useRouter()

// 页面状态
const isInitializing = ref(true)
const isRefreshing = ref(false)
const isLoadingEvent = ref(false)
const isLoadingTasks = ref(false)
const isLoadingAnalysis = ref(false)
const isLoadingKnowledge = ref(false)
const isSaving = ref(false)
const isSubmitting = ref(false)
const isViewMode = ref(false)


// 数据状态
const eventId = computed(() => route.params.id as string)
const selectedTaskId = ref('')
const currentEvent = ref(null)
const analysisTasks = ref([])

// 使用事件详情数据管理 - 暂时注释掉避免导入问题
// const {
//   eventDetailData,
//   loading: detailLoading,
//   errors: detailErrors,
//   hasPatientInfo,
//   hasDynamicFields,
//   hasAttachments,
//   isAnyLoading: isDetailLoading,
//   loadAllData: loadEventDetailData,
//   refreshData: refreshEventDetailData,
//   retryDataType
// } = useEventDetailData(eventId.value)

// 临时替代数据
const eventDetailData = ref({})
const detailLoading = ref(false)
const detailErrors = ref({})
const hasPatientInfo = ref(false)
const hasDynamicFields = ref(false)
const hasAttachments = ref(false)
const isDetailLoading = ref(false)
const loadEventDetailData = () => Promise.resolve()
const refreshEventDetailData = () => Promise.resolve()
const retryDataType = () => Promise.resolve()

// 详细信息显示控制
const showDetailedInfo = ref(false)
const showLegacyInfo = ref(false) // 控制是否显示原有组件
const showDebugInfo = ref(false) // 控制是否显示调试信息

// EventInfoDisplay组件配置
const eventFieldLabels = reactive({
  eventId: '事件编号',
  eventName: '事件名称',
  eventType: '事件类型',
  severity: '严重程度',
  status: '状态',
  patientName: '患者姓名',
  patientGender: '性别',
  patientAge: '年龄',
  reporterName: '上报人',
  reporterDepartment: '上报科室',
  reportTime: '上报时间',
  eventTime: '事件发生时间'
})

const eventFieldFormatters = reactive({
  timestamp: (value: number) => {
    if (!value) return '（未填写）'
    return dayjs(value).format('YYYY-MM-DD HH:mm:ss')
  },
  date: (value: string) => {
    if (!value) return '（未填写）'
    return dayjs(value).format('YYYY-MM-DD HH:mm:ss')
  },
  severity: (value: string) => {
    const severityMap: Record<string, string> = {
      '1': '低',
      '2': '中',
      '3': '高',
      '4': '紧急',
      '低': '低',
      '中': '中',
      '高': '高',
      '紧急': '紧急'
    }
    return severityMap[value] || value || '未知'
  },
  gender: (value: string) => {
    const genderMap: Record<string, string> = {
      'M': '男',
      'F': '女',
      '1': '男',
      '2': '女',
      '男': '男',
      '女': '女'
    }
    return genderMap[value] || value || '未知'
  }
})

const analysisData = reactive({
  analysisData: {
    meetingInfo: {},
    causes: [],
    measures: [],
    conclusion: {}
  },
  toolsData: {
    fishbone: {
      problem: '',
      mainBones: [],
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        version: '1.0.0'
      }
    },
    fiveWhy: {
      problem: '',
      whyLevels: [],
      conclusion: '',
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        version: '1.0.0'
      }
    },
    pdca: {
      cycleName: '',
      plan: [],
      do: [],
      check: [],
      act: [],
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        version: '1.0.0'
      }
    },
    fmea: {
      processName: '',
      riskItems: [],
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        version: '1.0.0'
      }
    }
  }
})

// 工具函数
const getSeverityColor = (severity: string) => {
  const colorMap = {
    'high': 'red',
    'medium': 'orange',
    'low': 'green',
    'critical': 'red',
    'major': 'orange',
    'minor': 'blue'
  }
  return colorMap[severity] || 'default'
}

const getSeverityText = (severity: string) => {
  const textMap = {
    'high': '高',
    'medium': '中',
    'low': '低',
    'critical': '严重',
    'major': '重大',
    'minor': '轻微'
  }
  return textMap[severity] || severity || '未知'
}

const getStatusColor = (status: string) => {
  const colorMap = {
    'pending': 'orange',
    'in_progress': 'blue',
    'completed': 'green',
    'cancelled': 'red',
    'draft': 'default'
  }
  return colorMap[status] || 'default'
}

const getStatusText = (status: string) => {
  const textMap = {
    'pending': '待开始',
    'in_progress': '进行中',
    'completed': '已完成',
    'cancelled': '已取消',
    'draft': '草稿'
  }
  return textMap[status] || status || '未知'
}

const getPriorityColor = (priority: string) => {
  const colorMap = {
    'high': 'red',
    'medium': 'orange',
    'low': 'green',
    'urgent': 'red',
    'normal': 'blue'
  }
  return colorMap[priority] || 'default'
}

const getPriorityText = (priority: string) => {
  const textMap = {
    'high': '高',
    'medium': '中',
    'low': '低',
    'urgent': '紧急',
    'normal': '普通'
  }
  return textMap[priority] || priority || '普通'
}

// 事件类型转换函数
function getEventTypeText(eventType: string) {
  const typeMap = {
    '1': '跌倒坠床事件',
    '2': '用药错误事件',
    '3': '医疗器械故障事件',
    '4': '感染控制事件',
    '5': '手术并发症事件',
    '6': '压疮事件'
  }
  return typeMap[eventType] || '其他事件'
}

// 事件处理函数
async function handleRefresh() {
  isRefreshing.value = true
  try {
    await Promise.all([
      loadEventData(),
      loadAnalysisTasks(),
      loadAnalysisData()
    ])
    message.success('刷新成功')
  } catch (error) {
    message.error('刷新失败')
  } finally {
    isRefreshing.value = false
  }
}

function handleBack() {
  router.push('/evt/analyze')
}

// 详细信息展示控制
function toggleDetailedInfo() {
  showDetailedInfo.value = !showDetailedInfo.value
}

// 文件操作处理
function handleFilePreview(file: any) {
  console.log('预览文件:', file)
  message.info(`预览文件: ${file.fileName}`)
}

function handleFileDownload(file: any) {
  console.log('下载文件:', file)
  message.success(`开始下载: ${file.fileName}`)
}

function handleFileReference(file: any) {
  console.log('引用文件:', file)
  message.success(`已将 ${file.fileName} 添加到分析引用中`)
}

// 新的统一组件事件处理函数
function handleEventDataLoaded(data: any) {
  console.log('✅ [EventAnalyzePage] 统一组件数据加载完成:', data)
  message.success('事件信息加载完成')
}

function handleEventFieldClick(field: string, value: any) {
  console.log('🔍 [EventAnalyzePage] 字段点击:', field, value)

  // 根据字段类型执行不同操作
  if (field === 'eventId') {
    message.info(`事件编号: ${value}`)
  } else if (field === 'patientName') {
    message.info(`患者姓名: ${value}`)
  } else {
    message.info(`字段 ${field}: ${value}`)
  }
}

function handleEventDataError(error: Error) {
  console.error('❌ [EventAnalyzePage] 统一组件数据加载失败:', error)
  message.error(`数据加载失败: ${error.message}`)
}

// EventInfoDisplay组件的其他事件处理函数
function handleEventSectionToggle(section: string, visible: boolean) {
  console.log('📋 [EventAnalyzePage] 节区切换:', section, visible ? '展开' : '折叠')
}

function handleEventConfigChange(config: any) {
  console.log('⚙️ [EventAnalyzePage] 配置变更:', config)
}

function handleEventDescriptionChange(content: string) {
  console.log('📝 [EventAnalyzePage] 描述变更:', content)
  // 这里可以实现自动保存功能
}

function handleEventAttachmentClick(attachment: any) {
  console.log('📎 [EventAnalyzePage] 附件点击:', attachment)
  message.info(`查看附件: ${attachment.name}`)
}





// 测试统一组件功能
function testEventInfoComponent() {
  console.log('🧪 [EventAnalyzePage] 测试统一事件信息组件')
  showDebugInfo.value = !showDebugInfo.value
  message.success('统一事件信息展示组件测试成功！')
}

// 重试加载详细信息
async function handleRetryDetailData() {
  console.log('🔄 EventAnalyzePage: 用户触发重试详细信息加载')

  try {
    // 检查哪些数据类型有错误，进行针对性重试
    const errorTypes = Object.keys(detailErrors).filter(key => detailErrors[key as keyof typeof detailErrors] !== null)

    if (errorTypes.length > 0) {
      console.log('🔍 EventAnalyzePage: 检测到错误的数据类型:', errorTypes)

      // 针对性重试失败的数据类型
      for (const errorType of errorTypes) {
        try {
          await retryDataType(errorType as keyof typeof detailErrors)
          console.log(`✅ EventAnalyzePage: ${errorType}数据重试成功`)
        } catch (error) {
          console.error(`❌ EventAnalyzePage: ${errorType}数据重试失败:`, error)
        }
      }
    } else {
      // 如果没有特定错误，刷新所有数据
      console.log('🔄 EventAnalyzePage: 刷新所有详细信息数据')
      await refreshEventDetailData()
    }

    message.success('数据重试完成')
  } catch (error) {
    console.error('❌ EventAnalyzePage: 重试详细信息失败:', error)
    message.error('重试失败，请稍后再试')
  }
}

// 处理打开分析工具
function handleOpenAnalysisTools() {
  // 在新标签页中打开分析工具页面
  const toolsUrl = router.resolve('/evt/analyze/tools').href
  window.open(toolsUrl, '_blank')
}

// 事件信息相关处理
async function handleEventRefresh() {
  await loadEventData()
}

function handleViewEventDetail(eventId: string) {
  // 跳转到事件详情页面
  router.push(`/evt/report/detail/${eventId}`)
}

// 任务相关处理
function handleTaskSelect(task: any) {
  selectedTaskId.value = task.id
  loadAnalysisData(task.id)
  message.success(`已选择任务：${task.title}`)
}

function handleTaskCreate() {
  message.info('创建新任务功能开发中...')
}

// 分析数据变化处理
function handleAnalysisDataChange(data: any) {
  Object.assign(analysisData, data)
  console.log('分析数据已更新:', data)
}

// 分析数据相关处理
async function handleSave() {
  isSaving.value = true
  try {
    // 模拟保存操作
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('分析数据已保存')
  } catch (error) {
    message.error('保存失败')
  } finally {
    isSaving.value = false
  }
}

async function handleSubmit() {
  isSubmitting.value = true
  try {
    // 模拟提交操作
    await new Promise(resolve => setTimeout(resolve, 1500))
    message.success('分析已提交')
  } catch (error) {
    message.error('提交失败')
  } finally {
    isSubmitting.value = false
  }
}

// 数据加载函数
async function loadEventData() {
  isLoadingEvent.value = true
  try {
    // 首先获取分析任务信息，从中获取事件ID
    const taskResponse = await getAnalyzeTaskDetail(eventId.value)
    if (!taskResponse || !taskResponse.eventId) {
      throw new Error('无法获取分析任务信息')
    }

    // 通过事件ID获取真实的事件信息
    const eventResponse = await getEventInfo(taskResponse.eventId)
    if (!eventResponse) {
      throw new Error('无法获取事件信息')
    }

    // 映射事件数据到前端格式
    currentEvent.value = {
      eventCode: eventResponse.id,
      eventName: eventResponse.evtname || '未知事件',
      eventType: getEventTypeText(eventResponse.evt_type),
      severity: 'medium', // 默认严重程度，可根据实际字段调整
      occurTime: eventResponse.ev_time ? formatDateTime(eventResponse.ev_time) : null,
      reportTime: eventResponse.rep_time ? formatDateTime(eventResponse.rep_time) : null,
      reporterName: eventResponse.reporterName || '未知',
      deptName: eventResponse.reportDeptName || '未知部门',
      description: eventResponse.sumnotes || eventResponse.evtname || '暂无描述'
    }

    // 并行加载详细信息数据
    await loadEventDetailData()

  } catch (error) {
    console.error('❌ EventAnalyzePage: 加载事件数据失败', error)
    message.error('加载事件数据失败')
    // 设置默认值以防止页面崩溃
    currentEvent.value = {
      eventCode: eventId.value,
      eventName: '加载失败',
      eventType: '未知',
      severity: 'medium',
      occurTime: null,
      reportTime: null,
      reporterName: '未知',
      deptName: '未知',
      description: '事件信息加载失败'
    }
  } finally {
    isLoadingEvent.value = false
  }
}

async function loadAnalysisTasks() {
  isLoadingTasks.value = true
  try {
    // 模拟加载任务数据
    await new Promise(resolve => setTimeout(resolve, 300))

    // 使用dayjs创建日期对象，确保与Ant Design Vue兼容
    const now = dayjs()
    const tomorrow = now.add(1, 'day')
    const dayAfterTomorrow = now.add(2, 'day')

    analysisTasks.value = [
      {
        id: eventId.value, // 使用真实的事件ID作为任务ID
        title: '初步分析',
        status: 'completed',
        priority: 'high',
        assignee: '李医生',
        deadline: tomorrow.toDate(), // 转换为Date对象
        createdAt: now.toDate(), // 转换为Date对象
        progress: 100
      },
      {
        id: eventId.value, // 使用真实的事件ID作为任务ID
        title: '深度分析',
        status: 'in_progress',
        priority: 'medium',
        assignee: '王护士',
        deadline: dayAfterTomorrow.toDate(), // 转换为Date对象
        createdAt: now.toDate(), // 转换为Date对象
        progress: 60
      }
    ]

    // 自动选择第一个进行中的任务
    const activeTask = analysisTasks.value.find(t => t.status === 'in_progress')
    if (activeTask) {
      selectedTaskId.value = activeTask.id
    }


  } catch (error) {
    console.error('❌ EventAnalyzePage: 加载任务数据失败', error)
    message.error('加载任务数据失败')
  } finally {
    isLoadingTasks.value = false
  }
}

async function loadAnalysisData(taskId?: string) {
  isLoadingAnalysis.value = true
  try {
    console.log('🔍 EventAnalyzePage: 开始加载分析数据', { taskId, eventId: eventId.value })

    // 获取真实的分析数据
    let analysisResponse = null

    try {
      // 尝试获取已存在的分析数据
      if (taskId) {
        analysisResponse = await getAnalysisDetails(taskId)
        console.log('✅ EventAnalyzePage: 获取到已存在的分析数据:', analysisResponse)
      }
    } catch (error) {
      console.log('ℹ️ EventAnalyzePage: 未找到已存在的分析数据，将创建新的分析记录')
    }

    // 构建分析数据结构
    const analysisData_new = {
      meetingInfo: {
        meetingTime: analysisResponse?.meetingTime ? formatDateTime(analysisResponse.meetingTime) : null,
        location: analysisResponse?.meetingLocation || '',
        moderator: analysisResponse?.moderator || '',
        participants: analysisResponse?.participants || ''
      },
      causes: analysisResponse?.causes || [],
      measures: analysisResponse?.measures || [],
      conclusion: {
        summary: analysisResponse?.conclusion || '',
        recommendations: analysisResponse?.recommendations || '',
        expectedOutcome: analysisResponse?.expectedOutcome || ''
      }
    }

    // 更新分析数据
    Object.assign(analysisData.analysisData, analysisData_new)

    // 初始化工具数据（基于真实事件信息）
    if (currentEvent.value) {
      const eventDescription = currentEvent.value.description || currentEvent.value.eventName || ''

      // 如果没有已存在的分析数据，使用事件信息初始化
      if (!analysisResponse) {
        analysisData.toolsData.fishbone.problem = eventDescription
        analysisData.toolsData.fiveWhy.problem = eventDescription
        analysisData.toolsData.pdca.cycleName = `${currentEvent.value.eventName} - PDCA分析`
        analysisData.toolsData.fmea.processName = `${currentEvent.value.eventName} - FMEA分析`
      } else {
        // 使用已存在的分析工具数据
        if (analysisResponse.analysisTools) {
          analysisResponse.analysisTools.forEach((tool: any) => {
            if (tool.toolType === 'fishbone' && tool.toolData) {
              Object.assign(analysisData.toolsData.fishbone, tool.toolData)
            } else if (tool.toolType === 'fiveWhy' && tool.toolData) {
              Object.assign(analysisData.toolsData.fiveWhy, tool.toolData)
            } else if (tool.toolType === 'pdca' && tool.toolData) {
              Object.assign(analysisData.toolsData.pdca, tool.toolData)
            } else if (tool.toolType === 'fmea' && tool.toolData) {
              Object.assign(analysisData.toolsData.fmea, tool.toolData)
            }
          })
        }
      }
    }

    console.log('✅ EventAnalyzePage: 分析数据加载完成', {
      hasExistingData: !!analysisResponse,
      meetingInfo: analysisData.analysisData.meetingInfo,
      causesCount: analysisData.analysisData.causes.length,
      measuresCount: analysisData.analysisData.measures.length
    })

  } catch (error) {
    const errorMsg = error instanceof Error ? error.message : '加载分析数据失败'
    console.error('❌ EventAnalyzePage: 加载分析数据失败', error)
    message.error(`加载分析数据失败: ${errorMsg}`)
  } finally {
    isLoadingAnalysis.value = false
  }
}

// 页面初始化
onMounted(async () => {
  try {
    isInitializing.value = true



    // 检查页面模式
    isViewMode.value = route.query.mode === 'view'

    // 分步加载数据，提供更好的错误处理
    try {
      await loadEventData()
    } catch (error) {
      console.error('❌ EventAnalyzePage: 事件数据加载失败', error)
      // 继续执行，不阻塞其他数据加载
    }

    try {
      await loadAnalysisTasks()
    } catch (error) {
      console.error('❌ EventAnalyzePage: 分析任务加载失败', error)
      // 继续执行，不阻塞其他数据加载
    }

    // 如果有选中的任务，加载分析数据
    if (selectedTaskId.value) {
      try {
        await loadAnalysisData(selectedTaskId.value)
      } catch (error) {
        console.error('❌ EventAnalyzePage: 分析数据加载失败', error)
      }
    }

  } catch (error) {
    console.error('❌ EventAnalyzePage: 页面初始化失败', error)
    message.error('页面初始化失败，请尝试刷新页面')
  } finally {
    isInitializing.value = false
  }
})
</script>

<style scoped lang="less">
.event-analyze-page {
  padding: 20px;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-shrink: 0;

    h1 {
      margin: 0;
      color: #1890ff;
      font-size: 24px;
    }
  }

  .loading-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;

    .loading-placeholder {
      width: 100%;
      height: 200px;
    }
  }

  .main-content {
    flex: 1;
    display: flex;
    gap: 20px;
    min-height: 0;

    .left-panel {
      width: 320px;
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      gap: 16px;
      overflow-y: auto;
    }

    .center-panel {
      flex: 1;
      min-width: 0;
      overflow: hidden;
    }

    .right-panel {
      width: 300px;
      flex-shrink: 0;
      overflow-y: auto;
    }
  }

  // 事件信息卡片样式
  .event-info-card {
    .event-info-content {
      .event-description {
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid #f0f0f0;

        h4 {
          margin: 0 0 8px 0;
          color: #666;
          font-size: 14px;
          font-weight: 500;
        }

        p {
          margin: 0;
          color: #333;
          line-height: 1.6;
        }
      }

      // 详细信息区域样式
      .detailed-info-section {
        margin-top: 16px;

        .unified-event-info {
          background: #fafafa;
          border-radius: 8px;
          padding: 16px;
          margin-bottom: 16px;

          // 统一组件样式增强
          :deep(.event-info-display) {
            .section-item {
              background: #ffffff;
              border: 1px solid #e8e8e8;
              border-radius: 6px;
              margin-bottom: 16px;
              transition: all 0.3s ease;

              &:hover {
                border-color: #1890ff;
                box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
              }

              .section-header {
                .section-title {
                  color: #1890ff;
                  font-weight: 600;
                }
              }
            }

            // 患者信息特殊样式
            .patient-info {
              .patient-avatar {
                background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
              }
            }

            // 字段点击效果
            .field-clickable {
              position: relative;

              &::after {
                content: '🔍';
                position: absolute;
                right: -20px;
                top: 50%;
                transform: translateY(-50%);
                opacity: 0;
                transition: opacity 0.2s ease;
              }

              &:hover::after {
                opacity: 1;
              }
            }
          }
        }
      }
    }
  }

  // 分析任务卡片样式
  .analysis-tasks-card {
    .task-list {
      .task-item {
        padding: 12px;
        border: 1px solid #f0f0f0;
        border-radius: 6px;
        margin-bottom: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .task-item:hover {
        border-color: #1890ff;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
      }

      .task-item.active {
        border-color: #1890ff;
        background-color: #f6ffed;

        .task-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .task-title {
            font-weight: 500;
            color: #333;
          }
        }

        .task-info {
          display: flex;
          flex-direction: column;
          gap: 4px;
          margin-bottom: 8px;

          .info-item {
            display: flex;
            align-items: center;
            font-size: 12px;

            .label {
              color: #666;
              margin-right: 4px;
            }

            .value {
              color: #333;
            }
          }
        }

        .task-progress {
          margin-top: 8px;
        }
      }
    }
  }

  // 知识库和协作讨论卡片样式
  .knowledge-card,
  .collaboration-card {
    margin-bottom: 16px;

    .knowledge-content,
    .collaboration-content {
      .message-list {
        max-height: 200px;
        overflow-y: auto;

        .message-item {
          padding: 8px 0;
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .message-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;

            .sender {
              font-weight: 500;
              color: #1890ff;
            }

            .time {
              font-size: 12px;
              color: #999;
            }
          }

          .message-content {
            color: #333;
            line-height: 1.5;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .event-analyze-page {
    .main-content {
      .left-panel {
        width: 280px;
      }

      .right-panel {
        width: 260px;
      }
    }
  }
}

@media (max-width: 992px) {
  .event-analyze-page {
    .main-content {
      flex-direction: column;
      gap: 16px;

      .left-panel,
      .right-panel {
        width: 100%;
        max-height: 400px;
      }

      .center-panel {
        order: 1;
        min-height: 500px;
      }

      .left-panel {
        order: 2;
        flex-direction: row;
        gap: 16px;
        max-height: none;
        overflow-x: auto;
        overflow-y: visible;

        > * {
          flex: 1;
          min-width: 300px;
        }
      }

      .right-panel {
        order: 3;
      }
    }
  }
}

@media (max-width: 768px) {
  .event-analyze-page {
    padding: 12px;

    .page-header {
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;

      h1 {
        font-size: 20px;
      }
    }

    .main-content {
      gap: 12px;

      .left-panel {
        flex-direction: column;
        overflow-x: visible;

        > * {
          min-width: auto;
        }
      }
    }
  }
}
</style>
