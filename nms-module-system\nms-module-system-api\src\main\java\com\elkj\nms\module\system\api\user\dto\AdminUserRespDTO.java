package com.elkj.nms.module.system.api.user.dto;

import com.elkj.nms.framework.common.enums.CommonStatusEnum;
import lombok.Data;

import java.time.LocalDate;
import java.util.Set;

/**
 * Admin 用户 Response DTO
 *
 * <AUTHOR>
 */
@Data
public class AdminUserRespDTO {

    /**
     * 用户ID
     */
    private Long id;
    /**
     * 用户账号
     */
    private String username;
    /**
     * 用户姓名
     */
    private String nickname;
    /**
     * 帐号状态
     *
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 机构ID
     */
    private Long orgId;
    /**
     * 岗位编号数组
     */
    private Set<Long> postIds;
    /**
     * 手机号码
     */
    private String mobile;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 性别
     */
    private Integer sex;
    /**
     * 护理层级
     */
    private Integer nurseLevel;
    /**
     * 人员职称
     */
    private Integer title;
    /**
     * 人员类别
     */
    private Integer category;
    /**
     * 人员类型
     */
    private Integer personType;
    /**
     * 出生日期
     */
    private LocalDate birthDate;
    /**
     * 单位入职时间
     */
    private LocalDate companyJoinDate;




}
