<!--
  EventInfoDisplay组件测试页面
  用于验证统一事件信息展示组件的功能
-->

<template>
  <div class="event-info-test-page">
    <div class="page-header">
      <h1>EventInfoDisplay 组件测试</h1>
      <a-space>
        <a-button type="primary" @click="loadTestData">加载测试数据</a-button>
        <a-button @click="switchMode">切换模式</a-button>
        <a-button @click="toggleDebug">调试信息</a-button>
      </a-space>
    </div>

    <div class="test-content">
      <!-- 测试控制面板 -->
      <a-card title="测试控制" style="margin-bottom: 16px;">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-select v-model:value="currentMode" placeholder="选择显示模式" style="width: 100%;">
              <a-select-option value="full">完整模式</a-select-option>
              <a-select-option value="simplified">简化模式</a-select-option>
              <a-select-option value="analysis">分析模式</a-select-option>
              <a-select-option value="audit">审核模式</a-select-option>
              <a-select-option value="readonly">只读模式</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="6">
            <a-select v-model:value="currentSize" placeholder="选择尺寸" style="width: 100%;">
              <a-select-option value="small">小</a-select-option>
              <a-select-option value="middle">中</a-select-option>
              <a-select-option value="large">大</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="6">
            <a-switch v-model:checked="bordered" checked-children="有边框" un-checked-children="无边框" />
          </a-col>
          <a-col :span="6">
            <a-switch v-model:checked="showSectionTitles" checked-children="显示标题" un-checked-children="隐藏标题" />
          </a-col>
        </a-row>
      </a-card>

      <!-- EventInfoDisplay组件测试 -->
      <a-card title="EventInfoDisplay 组件">
        <EventInfoDisplay
          :event-id="testEventId"
          :display-mode="currentMode"
          :data-source="'analysis'"
          :show-section-titles="showSectionTitles"
          :bordered="bordered"
          :size="currentSize"
          :auto-load="false"
          :clickable-fields="['eventId', 'patientName', 'reporterName']"
          :sensitive-fields="['patientName', 'hospitalNumber', 'phone']"
          :field-labels="fieldLabels"
          :field-formatters="fieldFormatters"
          @data-loaded="handleDataLoaded"
          @field-click="handleFieldClick"
          @section-toggle="handleSectionToggle"
          @error="handleError"
          @config-change="handleConfigChange"
          @description-change="handleDescriptionChange"
          @attachment-click="handleAttachmentClick"
        />
      </a-card>

      <!-- 调试信息 -->
      <a-card v-if="showDebug" title="调试信息" style="margin-top: 16px;">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="事件ID">{{ testEventId }}</a-descriptions-item>
          <a-descriptions-item label="显示模式">{{ currentMode }}</a-descriptions-item>
          <a-descriptions-item label="组件尺寸">{{ currentSize }}</a-descriptions-item>
          <a-descriptions-item label="边框">{{ bordered ? '是' : '否' }}</a-descriptions-item>
          <a-descriptions-item label="显示标题">{{ showSectionTitles ? '是' : '否' }}</a-descriptions-item>
          <a-descriptions-item label="最后事件">{{ lastEvent }}</a-descriptions-item>
        </a-descriptions>
        
        <div style="margin-top: 16px;">
          <h4>事件日志:</h4>
          <a-textarea 
            v-model:value="eventLog" 
            :rows="6" 
            readonly 
            style="font-family: monospace; font-size: 12px;"
          />
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import { EventInfoDisplay } from '@/components/EventInfo'

// 测试状态
const testEventId = ref('TEST-EVENT-123')
const currentMode = ref('analysis')
const currentSize = ref('middle')
const bordered = ref(true)
const showSectionTitles = ref(true)
const showDebug = ref(false)
const lastEvent = ref('无')
const eventLog = ref('组件测试页面已加载\n')

// 字段配置
const fieldLabels = reactive({
  eventId: '事件编号',
  eventName: '事件名称',
  patientName: '患者姓名',
  reporterName: '报告人',
  hospitalNumber: '住院号',
  phone: '联系电话'
})

const fieldFormatters = reactive({
  timestamp: (value: number) => {
    if (!value) return '（未填写）'
    return new Date(value).toLocaleString()
  },
  phone: (value: string) => {
    if (!value) return '（未填写）'
    return value.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
  }
})

// 事件处理方法
function handleDataLoaded(data: any) {
  lastEvent.value = '数据加载完成'
  eventLog.value += `[${new Date().toLocaleTimeString()}] 数据加载完成: ${JSON.stringify(data, null, 2)}\n`
  message.success('数据加载完成')
}

function handleFieldClick(field: string, value: any) {
  lastEvent.value = `字段点击: ${field}`
  eventLog.value += `[${new Date().toLocaleTimeString()}] 字段点击: ${field} = ${value}\n`
  message.info(`点击字段: ${field}`)
}

function handleSectionToggle(section: string, visible: boolean) {
  lastEvent.value = `区域切换: ${section}`
  eventLog.value += `[${new Date().toLocaleTimeString()}] 区域切换: ${section} = ${visible}\n`
}

function handleError(error: Error) {
  lastEvent.value = `错误: ${error.message}`
  eventLog.value += `[${new Date().toLocaleTimeString()}] 错误: ${error.message}\n`
  message.error(`组件错误: ${error.message}`)
}

function handleConfigChange(config: any) {
  lastEvent.value = '配置变更'
  eventLog.value += `[${new Date().toLocaleTimeString()}] 配置变更\n`
}

function handleDescriptionChange(content: string) {
  lastEvent.value = '描述变更'
  eventLog.value += `[${new Date().toLocaleTimeString()}] 描述变更: ${content.substring(0, 50)}...\n`
}

function handleAttachmentClick(attachment: any) {
  lastEvent.value = '附件点击'
  eventLog.value += `[${new Date().toLocaleTimeString()}] 附件点击: ${attachment.name}\n`
  message.info(`查看附件: ${attachment.name}`)
}

// 测试方法
function loadTestData() {
  eventLog.value += `[${new Date().toLocaleTimeString()}] 加载测试数据\n`
  message.info('加载测试数据（模拟）')
}

function switchMode() {
  const modes = ['full', 'simplified', 'analysis', 'audit', 'readonly']
  const currentIndex = modes.indexOf(currentMode.value)
  const nextIndex = (currentIndex + 1) % modes.length
  currentMode.value = modes[nextIndex]
  eventLog.value += `[${new Date().toLocaleTimeString()}] 切换模式: ${currentMode.value}\n`
  message.info(`切换到${currentMode.value}模式`)
}

function toggleDebug() {
  showDebug.value = !showDebug.value
  eventLog.value += `[${new Date().toLocaleTimeString()}] 调试信息: ${showDebug.value ? '显示' : '隐藏'}\n`
}
</script>

<style scoped>
.event-info-test-page {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  margin: 0;
  color: #262626;
}

.test-content {
  max-width: 1200px;
  margin: 0 auto;
}
</style>
