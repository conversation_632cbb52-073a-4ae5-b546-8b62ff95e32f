<template>
  <div class="event-info-display" :class="displayClass">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <a-spin size="large" tip="正在加载事件信息...">
        <div class="loading-placeholder"></div>
      </a-spin>
    </div>
    
    <!-- 错误状态 -->
    <div v-else-if="hasError" class="error-container">
      <a-result
        status="error"
        title="数据加载失败"
        :sub-title="loadingState.error?.message || '未知错误'"
      >
        <template #extra>
          <a-button type="primary" @click="handleRetry">
            重新加载
          </a-button>
        </template>
      </a-result>
    </div>
    
    <!-- 主要内容 -->
    <div v-else-if="isLoaded" class="content-container">
      <!-- 验证警告 -->
      <a-alert
        v-if="hasValidationWarnings"
        type="warning"
        :message="`数据验证警告 (${validationWarnings.length}项)`"
        :description="validationWarnings.join('; ')"
        show-icon
        closable
        class="validation-alert"
      />
      
      <!-- 基础信息 -->
      <EventBasicInfo
        v-if="displayConfig.sections.basicInfo.visible"
        :data="basicInfo"
        :layout="displayConfig.sections.basicInfo.layout"
        :title="displayConfig.sections.basicInfo.title || '基础信息'"
        :show-title="showSectionTitles"
        :collapsible="displayConfig.sections.basicInfo.collapsible"
        :default-collapsed="displayConfig.sections.basicInfo.defaultCollapsed"
        :bordered="bordered"
        :size="size"
        :show-empty="displayConfig.sections.basicInfo.showEmpty"
        :clickable-fields="clickableFields"
        :sensitive-fields="sensitiveFields"
        :field-labels="fieldLabels"
        :field-formatters="fieldFormatters"
        @field-click="handleFieldClick"
        @toggle-collapse="handleSectionToggle('basicInfo', $event)"
        class="section-item"
      />
      
      <!-- 患者信息 -->
      <PatientInfo
        v-if="displayConfig.sections.patientInfo.visible && patientInfo"
        :data="patientInfo"
        :layout="displayConfig.sections.patientInfo.layout"
        :title="displayConfig.sections.patientInfo.title || '患者信息'"
        :show-title="showSectionTitles"
        :collapsible="displayConfig.sections.patientInfo.collapsible"
        :default-collapsed="displayConfig.sections.patientInfo.defaultCollapsed"
        :bordered="bordered"
        :size="size"
        :show-empty="displayConfig.sections.patientInfo.showEmpty"
        :sensitive-mode="displayConfig.sections.patientInfo.sensitive"
        :clickable-fields="clickableFields"
        :hidden-fields="hiddenFields"
        @field-click="handleFieldClick"
        @toggle-collapse="handleSectionToggle('patientInfo', $event)"
        class="section-item"
      />
      
      <!-- 上报人信息 -->
      <ReporterInfo
        v-if="displayConfig.sections.reporterInfo.visible && reporterInfo"
        :data="reporterInfo"
        :layout="displayConfig.sections.reporterInfo.layout"
        :title="displayConfig.sections.reporterInfo.title || '上报人信息'"
        :show-title="showSectionTitles"
        :collapsible="displayConfig.sections.reporterInfo.collapsible"
        :default-collapsed="displayConfig.sections.reporterInfo.defaultCollapsed"
        :bordered="bordered"
        :size="size"
        :show-empty="displayConfig.sections.reporterInfo.showEmpty"
        :clickable-fields="clickableFields"
        @field-click="handleFieldClick"
        @toggle-collapse="handleSectionToggle('reporterInfo', $event)"
        class="section-item"
      />
      
      <!-- 时间线 -->
      <EventTimeline
        v-if="displayConfig.sections.timeline.visible && timeline"
        :data="timeline"
        :layout="displayConfig.sections.timeline.layout"
        :title="displayConfig.sections.timeline.title || '时间线'"
        :show-title="showSectionTitles"
        :collapsible="displayConfig.sections.timeline.collapsible"
        :default-collapsed="displayConfig.sections.timeline.defaultCollapsed"
        :size="size"
        @field-click="handleFieldClick"
        @toggle-collapse="handleSectionToggle('timeline', $event)"
        class="section-item"
      />
      
      <!-- 事件描述 -->
      <EventDescription
        v-if="displayConfig.sections.description.visible && description"
        :content="description"
        :title="displayConfig.sections.description.title || '事件描述'"
        :show-title="showSectionTitles"
        :collapsible="displayConfig.sections.description.collapsible"
        :default-collapsed="displayConfig.sections.description.defaultCollapsed"
        :max-length="displayConfig.sections.description.maxLength"
        :readonly="readonly"
        @content-change="handleDescriptionChange"
        @toggle-collapse="handleSectionToggle('description', $event)"
        class="section-item"
      />
      
      <!-- 动态字段 -->
      <DynamicFields
        v-if="displayConfig.sections.dynamicFields.visible && Object.keys(dynamicFields).length > 0"
        :data="dynamicFields"
        :layout="displayConfig.sections.dynamicFields.layout"
        :title="displayConfig.sections.dynamicFields.title || '其他信息'"
        :show-title="showSectionTitles"
        :collapsible="displayConfig.sections.dynamicFields.collapsible"
        :default-collapsed="displayConfig.sections.dynamicFields.defaultCollapsed"
        :bordered="bordered"
        :size="size"
        :show-empty="displayConfig.sections.dynamicFields.showEmpty"
        :clickable-fields="clickableFields"
        :hidden-fields="hiddenFields"
        @field-click="handleFieldClick"
        @toggle-collapse="handleSectionToggle('dynamicFields', $event)"
        class="section-item"
      />
      
      <!-- 附件 -->
      <AttachmentList
        v-if="displayConfig.sections.attachments.visible && attachments.length > 0"
        :data="attachments"
        :title="displayConfig.sections.attachments.title || '相关附件'"
        :show-title="showSectionTitles"
        :collapsible="displayConfig.sections.attachments.collapsible"
        :default-collapsed="displayConfig.sections.attachments.defaultCollapsed"
        :readonly="readonly"
        @attachment-click="handleAttachmentClick"
        @toggle-collapse="handleSectionToggle('attachments', $event)"
        class="section-item"
      />
      
      <!-- 审核信息 -->
      <AuditInfo
        v-if="displayConfig.sections.auditInfo.visible && auditInfo"
        :data="auditInfo"
        :layout="displayConfig.sections.auditInfo.layout"
        :title="displayConfig.sections.auditInfo.title || '审核信息'"
        :show-title="showSectionTitles"
        :collapsible="displayConfig.sections.auditInfo.collapsible"
        :default-collapsed="displayConfig.sections.auditInfo.defaultCollapsed"
        :bordered="bordered"
        :size="size"
        @field-click="handleFieldClick"
        @toggle-collapse="handleSectionToggle('auditInfo', $event)"
        class="section-item"
      />
      
      <!-- 自定义节区 -->
      <component
        v-for="section in customSections"
        :key="section.key"
        :is="section.component"
        v-bind="section.props"
        v-show="section.visible"
        @field-click="handleFieldClick"
        class="section-item custom-section"
      />
    </div>
    
    <!-- 空状态 -->
    <div v-else class="empty-container">
      <a-empty description="暂无事件信息" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch, onMounted } from 'vue'
import type { 
  DisplayMode, 
  DataSource, 
  UnifiedEventData,
  EventInfoEvents 
} from './types'
import { useEventInfo } from './composables/useEventInfo'
import { useDisplayConfig } from './composables/useDisplayConfig'
import EventBasicInfo from './components/EventBasicInfo.vue'
import PatientInfo from './components/PatientInfo.vue'
import ReporterInfo from './components/ReporterInfo.vue'
import EventTimeline from './components/EventTimeline.vue'
import EventDescription from './components/EventDescription.vue'
import DynamicFields from './components/DynamicFields.vue'
import AttachmentList from './components/AttachmentList.vue'
import AuditInfo from './components/AuditInfo.vue'

interface Props {
  eventId?: string
  dataSource?: DataSource
  displayMode?: DisplayMode
  showSectionTitles?: boolean
  bordered?: boolean
  size?: 'small' | 'middle' | 'large'
  readonly?: boolean
  autoLoad?: boolean
  clickableFields?: string[]
  sensitiveFields?: string[]
  hiddenFields?: string[]
  fieldLabels?: Record<string, string>
  fieldFormatters?: Record<string, (value: any) => string>
}

interface Emits {
  (e: 'data-loaded', data: UnifiedEventData): void
  (e: 'field-click', field: string, value: any): void
  (e: 'section-toggle', section: string, visible: boolean): void
  (e: 'error', error: Error): void
  (e: 'config-change', config: any): void
  (e: 'description-change', content: string): void
  (e: 'attachment-click', attachment: any): void
}

const props = withDefaults(defineProps<Props>(), {
  displayMode: 'full',
  dataSource: 'auto',
  showSectionTitles: true,
  bordered: true,
  size: 'middle',
  readonly: false,
  autoLoad: true,
  clickableFields: () => [],
  sensitiveFields: () => [],
  hiddenFields: () => [],
  fieldLabels: () => ({}),
  fieldFormatters: () => ({})
})

const emit = defineEmits<Emits>()

// 使用 Composables
const {
  eventData,
  loadingState,
  validationResult,
  isLoading,
  hasError,
  isLoaded,
  hasValidationErrors,
  basicInfo,
  patientInfo,
  reporterInfo,
  timeline,
  description,
  dynamicFields,
  attachments,
  auditInfo,
  loadEventData,
  retryLoad,
  getValidationWarnings
} = useEventInfo(props.eventId, props.dataSource)

const {
  currentMode,
  config: displayConfig,
  customSections,
  switchMode
} = useDisplayConfig(props.displayMode)

// 计算属性
const displayClass = computed(() => ({
  [`mode-${currentMode.value}`]: true,
  [`size-${props.size}`]: true,
  'bordered': props.bordered,
  'readonly': props.readonly,
  'compact': displayConfig.customization?.compactMode
}))

const hasValidationWarnings = computed(() => {
  return validationResult.value && validationResult.value.warnings.length > 0
})

const validationWarnings = computed(() => {
  return getValidationWarnings()
})

// 方法
const handleRetry = async () => {
  try {
    await retryLoad()
  } catch (error) {
    emit('error', error as Error)
  }
}

const handleFieldClick = (field: string, value: any) => {
  emit('field-click', field, value)
}

const handleSectionToggle = (section: string, collapsed: boolean) => {
  emit('section-toggle', section, !collapsed)
}

const handleDescriptionChange = (content: string) => {
  emit('description-change', content)
}

const handleAttachmentClick = (attachment: any) => {
  emit('attachment-click', attachment)
}

// 监听器
watch(() => props.displayMode, (newMode) => {
  switchMode(newMode)
})

watch(() => props.eventId, (newEventId) => {
  if (newEventId && props.autoLoad) {
    loadEventData(newEventId, props.dataSource)
  }
})

watch(eventData, (newData) => {
  if (newData) {
    emit('data-loaded', newData)
  }
})

watch(displayConfig, (newConfig) => {
  emit('config-change', newConfig)
}, { deep: true })

// 生命周期
onMounted(() => {
  if (props.eventId && props.autoLoad) {
    loadEventData(props.eventId, props.dataSource)
  }
})

// 暴露方法给父组件
defineExpose({
  loadEventData,
  retryLoad,
  switchMode,
  eventData,
  loadingState,
  validationResult
})
</script>

<style scoped>
/* 统一事件信息展示组件样式 */
.event-info-display {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.5715;
  color: #262626;
}

.event-info-display * {
  box-sizing: border-box;
}

/* 加载状态 */
.event-info-display .loading-container {
  padding: 60px 0;
  text-align: center;
}

.event-info-display .loading-container .loading-placeholder {
  height: 200px;
}

/* 错误状态 */
.event-info-display .error-container {
  padding: 40px 0;
  text-align: center;
}

/* 内容容器 */
.event-info-display .content-container .validation-alert {
  margin-bottom: 16px;
  border-radius: 6px;
}

.event-info-display .content-container .section-item {
  margin-bottom: 24px;
  padding: 16px;
  background: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.event-info-display .content-container .section-item:last-child {
  margin-bottom: 0;
}

.event-info-display .content-container .section-item:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.event-info-display .content-container .section-item.custom-section {
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  padding: 16px;
  background: #fafafa;
}

/* 空状态 */
.event-info-display .empty-container {
  padding: 60px 0;
  text-align: center;
  color: #8c8c8c;
}

/* 模式样式 */
.event-info-display.mode-simplified .section-item {
  margin-bottom: 16px;
  padding: 12px;
}

.event-info-display.mode-readonly {
  pointer-events: none;
  opacity: 0.8;
}

.event-info-display.mode-readonly .section-item {
  background: #f9f9f9;
  border-color: #e8e8e8;
}

.event-info-display.compact .section-item {
  margin-bottom: 12px;
  padding: 12px;
}

/* 尺寸样式 */
.event-info-display.size-small {
  font-size: 12px;
}

.event-info-display.size-small .section-item {
  margin-bottom: 16px;
  padding: 12px;
}

.event-info-display.size-large {
  font-size: 16px;
}

.event-info-display.size-large .section-item {
  margin-bottom: 32px;
  padding: 20px;
}

/* 边框样式 */
.event-info-display.bordered .section-item {
  border: 1px solid #d9d9d9;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .event-info-display .content-container .section-item {
    margin-bottom: 16px;
    padding: 12px;
  }

  .event-info-display.size-large .section-item {
    margin-bottom: 20px;
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .event-info-display .content-container .section-item {
    margin-bottom: 12px;
    padding: 8px;
  }
}
</style>
