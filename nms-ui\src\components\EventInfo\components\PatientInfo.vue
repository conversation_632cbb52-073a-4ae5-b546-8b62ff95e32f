<template>
  <div class="patient-info" :class="layoutClass">
    <div v-if="showTitle" class="section-header">
      <h4 class="section-title">
        <UserOutlined class="title-icon" />
        {{ title }}
      </h4>
      <div class="header-actions">
        <a-tag v-if="patientData?.gender" :color="genderColor">
          {{ patientData.gender }}
        </a-tag>
        <a-tag v-if="patientData?.age" color="blue">
          {{ patientData.age }}岁
        </a-tag>
        <a-button 
          v-if="collapsible" 
          type="text" 
          size="small"
          @click="toggleCollapse"
        >
          <template #icon>
            <DownOutlined v-if="!collapsed" />
            <RightOutlined v-else />
          </template>
        </a-button>
      </div>
    </div>
    
    <div v-show="!collapsed" class="section-content">
      <!-- 无患者信息提示 -->
      <a-empty 
        v-if="!hasPatientData"
        :image="Empty.PRESENTED_IMAGE_SIMPLE"
        description="暂无患者信息"
        class="empty-state"
      />
      
      <!-- 患者信息内容 -->
      <div v-else>
        <!-- 表格布局 -->
        <a-descriptions 
          v-if="layout === 'table'"
          :bordered="bordered"
          :column="columns"
          :size="size"
          :labelStyle="labelStyle"
        >
          <a-descriptions-item 
            v-for="field in visibleFields" 
            :key="field.key"
            :label="field.label"
            :span="field.span"
          >
            <span 
              :class="getFieldClass(field)"
              @click="handleFieldClick(field.key, field.value)"
            >
              {{ formatFieldValue(field) }}
            </span>
          </a-descriptions-item>
        </a-descriptions>
        
        <!-- 卡片布局 -->
        <div v-else-if="layout === 'card'" class="card-layout">
          <div class="patient-avatar">
            <a-avatar :size="64" :style="avatarStyle">
              {{ patientData?.name?.charAt(0) || '患' }}
            </a-avatar>
            <div class="patient-basic">
              <h5 class="patient-name">{{ patientData?.name || '未知患者' }}</h5>
              <div class="patient-meta">
                <span v-if="patientData?.gender">{{ patientData.gender }}</span>
                <span v-if="patientData?.age">{{ patientData.age }}岁</span>
                <span v-if="patientData?.hospitalNumber">{{ patientData.hospitalNumber }}</span>
              </div>
            </div>
          </div>
          
          <div class="patient-details">
            <div 
              v-for="field in detailFields" 
              :key="field.key"
              class="detail-item"
              @click="handleFieldClick(field.key, field.value)"
            >
              <div class="detail-label">{{ field.label }}</div>
              <div class="detail-value" :class="getFieldClass(field)">
                {{ formatFieldValue(field) }}
              </div>
            </div>
          </div>
        </div>
        
        <!-- 内联布局 -->
        <div v-else-if="layout === 'inline'" class="inline-layout">
          <span 
            v-for="(field, index) in visibleFields" 
            :key="field.key"
            class="inline-field"
            @click="handleFieldClick(field.key, field.value)"
          >
            <span class="field-label">{{ field.label }}:</span>
            <span class="field-value" :class="getFieldClass(field)">
              {{ formatFieldValue(field) }}
            </span>
            <span v-if="index < visibleFields.length - 1" class="field-separator">|</span>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { UserOutlined, DownOutlined, RightOutlined } from '@ant-design/icons-vue'
import { Empty } from 'ant-design-vue'
import type { LayoutType } from '../types'

interface PatientData {
  name?: string
  gender?: string
  age?: number
  hospitalNumber?: string
  department?: string
  bedNumber?: string
  diagnosis?: string
  admissionDate?: string
  [key: string]: any
}

interface FieldData {
  key: string
  label: string
  value: any
  type?: string
  sensitive?: boolean
  clickable?: boolean
  span?: number
}

interface Props {
  data?: PatientData
  layout?: LayoutType
  title?: string
  showTitle?: boolean
  collapsible?: boolean
  defaultCollapsed?: boolean
  bordered?: boolean
  columns?: number
  size?: 'small' | 'middle' | 'large'
  showEmpty?: boolean
  sensitiveMode?: boolean
  clickableFields?: string[]
  hiddenFields?: string[]
}

interface Emits {
  (e: 'field-click', field: string, value: any): void
  (e: 'toggle-collapse', collapsed: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  layout: 'card',
  title: '患者信息',
  showTitle: true,
  collapsible: false,
  defaultCollapsed: false,
  bordered: true,
  columns: 2,
  size: 'middle',
  showEmpty: true,
  sensitiveMode: false,
  clickableFields: () => [],
  hiddenFields: () => []
})

const emit = defineEmits<Emits>()

// 响应式状态
const collapsed = ref(props.defaultCollapsed)

// 计算属性
const layoutClass = computed(() => `layout-${props.layout}`)
const patientData = computed(() => props.data)
const hasPatientData = computed(() => !!patientData.value && Object.keys(patientData.value).length > 0)

const labelStyle = computed(() => ({
  width: props.layout === 'table' ? '100px' : 'auto'
}))

const genderColor = computed(() => {
  if (!patientData.value?.gender) return 'default'
  return patientData.value.gender === '男' ? 'blue' : 'pink'
})

const avatarStyle = computed(() => ({
  backgroundColor: patientData.value?.gender === '男' ? '#1890ff' : '#eb2f96'
}))

// 字段标签映射
const fieldLabels: Record<string, string> = {
  name: '姓名',
  gender: '性别',
  age: '年龄',
  hospitalNumber: '住院号',
  department: '科室',
  bedNumber: '床号',
  diagnosis: '诊断',
  admissionDate: '入院日期'
}

// 敏感字段列表
const sensitiveFields = ['name', 'hospitalNumber']

// 可见字段列表
const visibleFields = computed(() => {
  if (!hasPatientData.value) return []
  
  const allFields = Object.keys(patientData.value!)
    .filter(key => !props.hiddenFields.includes(key))
    .filter(key => {
      const value = patientData.value![key]
      return props.showEmpty || (value !== null && value !== undefined && value !== '')
    })
  
  return allFields.map(key => ({
    key,
    label: fieldLabels[key] || key,
    value: patientData.value![key],
    type: typeof patientData.value![key],
    sensitive: props.sensitiveMode && sensitiveFields.includes(key),
    clickable: props.clickableFields.includes(key),
    span: 1
  }))
})

// 详情字段（排除基础字段）
const detailFields = computed(() => {
  const basicFields = ['name', 'gender', 'age', 'hospitalNumber']
  return visibleFields.value.filter(field => !basicFields.includes(field.key))
})

// 方法
const toggleCollapse = () => {
  collapsed.value = !collapsed.value
  emit('toggle-collapse', collapsed.value)
}

const handleFieldClick = (field: string, value: any) => {
  if (props.clickableFields.includes(field)) {
    emit('field-click', field, value)
  }
}

const getFieldClass = (field: FieldData) => {
  return {
    'field-sensitive': field.sensitive,
    'field-clickable': field.clickable,
    'field-empty': !field.value
  }
}

const formatFieldValue = (field: FieldData): string => {
  // 敏感信息处理
  if (field.sensitive && field.value) {
    return maskSensitiveData(field.value, field.key)
  }
  
  // 空值处理
  if (field.value === null || field.value === undefined || field.value === '') {
    return '（未填写）'
  }
  
  // 特殊字段格式化
  if (field.key === 'age' && typeof field.value === 'number') {
    return `${field.value}岁`
  }
  
  if (field.key === 'admissionDate' && field.value) {
    return new Date(field.value).toLocaleDateString('zh-CN')
  }
  
  return String(field.value)
}

const maskSensitiveData = (value: string, fieldKey: string): string => {
  if (fieldKey === 'name') {
    if (value.length <= 2) return value
    return value.charAt(0) + '*'.repeat(value.length - 2) + value.charAt(value.length - 1)
  }
  if (fieldKey === 'hospitalNumber') {
    return value.replace(/(.{2}).*(.{2})/, '$1****$2')
  }
  return value
}
</script>

<style scoped>
.patient-info {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #262626;
      
      .title-icon {
        color: #1890ff;
      }
    }
    
    .header-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
  
  .empty-state {
    padding: 40px 0;
  }
  
  /* 卡片布局样式 */
}

.patient-info.layout-card {
    .card-layout {
      .patient-avatar {
        display: flex;
        align-items: center;
        gap: 16px;
        padding: 20px;
        background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%);
        border-radius: 12px;
        margin-bottom: 16px;
        
        .patient-basic {
          flex: 1;
          
          .patient-name {
            margin: 0 0 8px 0;
            font-size: 18px;
            font-weight: 600;
            color: #262626;
          }
          
          .patient-meta {
            display: flex;
            gap: 12px;
            font-size: 14px;
            color: #8c8c8c;
            
            span {
              padding: 2px 8px;
              background: rgba(255, 255, 255, 0.8);
              border-radius: 4px;
            }
          }
        }
      }
      
      .patient-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 12px;
        
        .detail-item {
          padding: 12px;
          border: 1px solid #f0f0f0;
          border-radius: 8px;
          background: #fafafa;
          transition: all 0.2s ease;
          
          &:hover {
            border-color: #d9d9d9;
            background: #f5f5f5;
          }
          
          .detail-label {
            font-size: 12px;
            color: #8c8c8c;
            margin-bottom: 4px;
          }
          
          .detail-value {
            font-size: 14px;
            color: #262626;
            font-weight: 500;
          }
        }
      }
    }
  }

/* 内联布局样式 */
.patient-info.layout-inline {
    .inline-layout {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      
      .inline-field {
        display: inline-flex;
        align-items: center;
        gap: 4px;
        
        .field-label {
          font-size: 14px;
          color: #8c8c8c;
        }
        
        .field-value {
          font-size: 14px;
          color: #262626;
          font-weight: 500;
        }
        
        .field-separator {
          margin-left: 8px;
          color: #d9d9d9;
        }
      }
    }
  }

/* 字段状态样式 */
.patient-info .field-sensitive {
  color: #ff7875 !important;
  font-family: monospace;
}

.patient-info .field-clickable {
  cursor: pointer;
  color: #1890ff;
}

.patient-info .field-clickable:hover {
  text-decoration: underline;
}
.patient-info .field-empty {
  color: #bfbfbf;
  font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .patient-info.layout-card {
      .patient-avatar {
        flex-direction: column;
        text-align: center;
        
        .patient-meta {
          justify-content: center;
        }
      }
      
      .patient-details {
        grid-template-columns: 1fr;
      }
    }

  .patient-info.layout-inline .inline-layout {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
