<template>
  <div class="event-basic-info" :class="layoutClass">
    <div v-if="showTitle" class="section-header">
      <h4 class="section-title">{{ title }}</h4>
      <a-button 
        v-if="collapsible" 
        type="text" 
        size="small"
        @click="toggleCollapse"
      >
        <template #icon>
          <DownOutlined v-if="!collapsed" />
          <RightOutlined v-else />
        </template>
        {{ collapsed ? '展开' : '收起' }}
      </a-button>
    </div>
    
    <div v-show="!collapsed" class="section-content">
      <!-- 表格布局 -->
      <a-descriptions 
        v-if="layout === 'table'"
        :bordered="bordered"
        :column="columns"
        :size="size"
        :labelStyle="labelStyle"
      >
        <a-descriptions-item 
          v-for="field in visibleFields" 
          :key="field.key"
          :label="field.label"
          :span="field.span"
        >
          <span 
            :class="getFieldClass(field)"
            @click="handleFieldClick(field.key, field.value)"
          >
            {{ formatFieldValue(field) }}
          </span>
        </a-descriptions-item>
      </a-descriptions>
      
      <!-- 卡片布局 -->
      <div v-else-if="layout === 'card'" class="card-layout">
        <div 
          v-for="field in visibleFields" 
          :key="field.key"
          class="field-card"
          @click="handleFieldClick(field.key, field.value)"
        >
          <div class="field-label">{{ field.label }}</div>
          <div class="field-value" :class="getFieldClass(field)">
            {{ formatFieldValue(field) }}
          </div>
        </div>
      </div>
      
      <!-- 内联布局 -->
      <div v-else-if="layout === 'inline'" class="inline-layout">
        <span 
          v-for="(field, index) in visibleFields" 
          :key="field.key"
          class="inline-field"
          @click="handleFieldClick(field.key, field.value)"
        >
          <span class="field-label">{{ field.label }}:</span>
          <span class="field-value" :class="getFieldClass(field)">
            {{ formatFieldValue(field) }}
          </span>
          <span v-if="index < visibleFields.length - 1" class="field-separator">|</span>
        </span>
      </div>
      
      <!-- 网格布局 -->
      <div v-else-if="layout === 'grid'" class="grid-layout">
        <div 
          v-for="field in visibleFields" 
          :key="field.key"
          class="grid-item"
          @click="handleFieldClick(field.key, field.value)"
        >
          <div class="field-label">{{ field.label }}</div>
          <div class="field-value" :class="getFieldClass(field)">
            {{ formatFieldValue(field) }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { DownOutlined, RightOutlined } from '@ant-design/icons-vue'
import type { LayoutType } from '../types'

interface FieldData {
  key: string
  label: string
  value: any
  type?: string
  sensitive?: boolean
  clickable?: boolean
  span?: number
}

interface Props {
  data?: Record<string, any>
  fields?: string[]
  layout?: LayoutType
  title?: string
  showTitle?: boolean
  collapsible?: boolean
  defaultCollapsed?: boolean
  bordered?: boolean
  columns?: number
  size?: 'small' | 'middle' | 'large'
  showEmpty?: boolean
  clickableFields?: string[]
  sensitiveFields?: string[]
  fieldLabels?: Record<string, string>
  fieldFormatters?: Record<string, (value: any) => string>
}

interface Emits {
  (e: 'field-click', field: string, value: any): void
  (e: 'toggle-collapse', collapsed: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  layout: 'table',
  title: '基础信息',
  showTitle: true,
  collapsible: false,
  defaultCollapsed: false,
  bordered: true,
  columns: 2,
  size: 'middle',
  showEmpty: true,
  fields: () => [],
  clickableFields: () => [],
  sensitiveFields: () => [],
  fieldLabels: () => ({}),
  fieldFormatters: () => ({})
})

const emit = defineEmits<Emits>()

// 响应式状态
const collapsed = ref(props.defaultCollapsed)

// 计算属性
const layoutClass = computed(() => `layout-${props.layout}`)

const labelStyle = computed(() => ({
  width: props.layout === 'table' ? '120px' : 'auto'
}))

// 默认字段标签映射
const defaultFieldLabels: Record<string, string> = {
  eventId: '事件编号',
  eventName: '事件名称',
  eventType: '事件类型',
  severity: '严重程度',
  status: '状态',
  description: '事件描述'
}

// 可见字段列表
const visibleFields = computed(() => {
  if (!props.data) return []
  
  const fieldsToShow = props.fields.length > 0 ? props.fields : Object.keys(props.data)
  
  return fieldsToShow
    .filter(key => {
      const value = props.data![key]
      return props.showEmpty || (value !== null && value !== undefined && value !== '')
    })
    .map(key => ({
      key,
      label: props.fieldLabels[key] || defaultFieldLabels[key] || key,
      value: props.data![key],
      type: typeof props.data![key],
      sensitive: props.sensitiveFields.includes(key),
      clickable: props.clickableFields.includes(key),
      span: 1
    }))
})

// 方法
const toggleCollapse = () => {
  collapsed.value = !collapsed.value
  emit('toggle-collapse', collapsed.value)
}

const handleFieldClick = (field: string, value: any) => {
  if (props.clickableFields.includes(field)) {
    emit('field-click', field, value)
  }
}

const getFieldClass = (field: FieldData) => {
  return {
    'field-sensitive': field.sensitive,
    'field-clickable': field.clickable,
    'field-empty': !field.value
  }
}

const formatFieldValue = (field: FieldData): string => {
  // 使用自定义格式化器
  if (props.fieldFormatters[field.key]) {
    return props.fieldFormatters[field.key](field.value)
  }
  
  // 敏感信息处理
  if (field.sensitive && field.value) {
    return maskSensitiveData(field.value, field.key)
  }
  
  // 空值处理
  if (field.value === null || field.value === undefined || field.value === '') {
    return '（未填写）'
  }
  
  // 默认格式化
  return String(field.value)
}

const maskSensitiveData = (value: string, fieldKey: string): string => {
  if (fieldKey.includes('phone') || fieldKey.includes('mobile')) {
    return value.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
  }
  if (fieldKey.includes('id') || fieldKey.includes('number')) {
    return value.replace(/(.{2}).*(.{2})/, '$1****$2')
  }
  return value.replace(/(.{1}).*(.{1})/, '$1****$2')
}
</script>

<style scoped>
.event-basic-info {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .section-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }
  }
  
  .section-content {
    transition: all 0.3s ease;
  }
  
  /* 卡片布局样式 */
}

.event-basic-info.layout-card .card-layout {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.event-basic-info.layout-card .field-card {
  padding: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.2s ease;
}

.event-basic-info.layout-card .field-card:hover {
  border-color: #40a9ff;
  background: #f0f8ff;
}

.event-basic-info.layout-card .field-label {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.event-basic-info.layout-card .field-value {
  font-size: 14px;
  color: #262626;
  font-weight: 500;
}

/* 内联布局样式 */
.event-basic-info.layout-inline .inline-layout {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.event-basic-info.layout-inline .inline-field {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.event-basic-info.layout-inline .field-label {
  font-size: 14px;
  color: #8c8c8c;
}

.event-basic-info.layout-inline .field-value {
  font-size: 14px;
  color: #262626;
  font-weight: 500;
}

.event-basic-info.layout-inline .field-separator {
  margin-left: 8px;
  color: #d9d9d9;
}

/* 网格布局样式 */
.event-basic-info.layout-grid .grid-layout {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 16px;
}

.event-basic-info.layout-grid .grid-item {
  text-align: center;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  background: #fafafa;
}

.event-basic-info.layout-grid .field-label {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.event-basic-info.layout-grid .field-value {
  font-size: 16px;
  color: #262626;
  font-weight: 600;
}

/* 字段状态样式 */
.event-basic-info .field-sensitive {
  color: #ff7875 !important;
  font-family: monospace;
}

.event-basic-info .field-clickable {
  cursor: pointer;
  color: #1890ff;
}

.event-basic-info .field-clickable:hover {
  text-decoration: underline;
}

.event-basic-info .field-empty {
  color: #bfbfbf;
  font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .event-basic-info.layout-card .card-layout {
    grid-template-columns: 1fr;
  }

  .event-basic-info.layout-inline .inline-layout {
    flex-direction: column;
    gap: 8px;
  }

  .event-basic-info.layout-grid .grid-layout {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
