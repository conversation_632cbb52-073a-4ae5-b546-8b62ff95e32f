<template>
  <div class="event-analyze-page">
    <h1>事件分析详情页面 - 简化版本</h1>
    <p>事件ID: {{ eventId }}</p>
    <p>页面路径: {{ route.path }}</p>
    
    <a-card title="测试页面">
      <p>这是一个简化的事件分析页面，用于测试路由是否正常工作。</p>
      <a-button type="primary" @click="handleBack">返回列表</a-button>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

// 路由相关
const route = useRoute()
const router = useRouter()

// 事件ID
const eventId = computed(() => route.params.id as string)

// 返回列表
function handleBack() {
  router.push('/evt/analyze')
}
</script>

<style scoped>
.event-analyze-page {
  padding: 20px;
}

.event-analyze-page h1 {
  color: #1890ff;
  margin-bottom: 16px;
}

.event-analyze-page p {
  margin-bottom: 8px;
}
</style>
