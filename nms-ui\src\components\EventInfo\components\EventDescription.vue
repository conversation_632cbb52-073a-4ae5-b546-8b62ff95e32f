<template>
  <div class="event-description" :class="componentClass">
    <!-- 加载状态 -->
    <div v-if="loading" class="description-loading">
      <a-spin size="small" />
      <span>加载事件描述...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="description-error">
      <a-alert type="error" :message="error" show-icon />
    </div>

    <!-- 空状态 -->
    <div v-else-if="!description || description.trim() === ''" class="description-empty">
      <a-empty description="暂无事件描述" />
    </div>

    <!-- 正常显示 -->
    <div v-else class="description-content">
      <!-- 卡片布局 -->
      <a-card v-if="layout === 'card'" :bordered="bordered" :size="size">
        <template #title>
          <div class="description-title">
            <FileTextOutlined />
            <span>事件描述</span>
            <a-tag v-if="wordCount > 0" color="blue" size="small">
              {{ wordCount }} 字
            </a-tag>
          </div>
        </template>
        
        <template #extra v-if="showActions">
          <a-space>
            <a-button 
              v-if="collapsible" 
              type="text" 
              size="small"
              @click="toggleExpanded"
            >
              {{ isExpanded ? '收起' : '展开' }}
              <component :is="isExpanded ? 'UpOutlined' : 'DownOutlined'" />
            </a-button>
            
            <a-button 
              v-if="copyable" 
              type="text" 
              size="small"
              @click="copyDescription"
            >
              <CopyOutlined />
              复制
            </a-button>
          </a-space>
        </template>
        
        <div class="description-text" :class="{ 'description-collapsed': collapsible && !isExpanded }">
          <div v-if="highlightKeywords.length > 0" v-html="highlightedDescription"></div>
          <div v-else>{{ description }}</div>
        </div>
        
        <div v-if="collapsible && !isExpanded && description.length > maxLength" class="description-more">
          <a-button type="link" size="small" @click="toggleExpanded">
            查看更多...
          </a-button>
        </div>
      </a-card>

      <!-- 简单布局 -->
      <div v-else-if="layout === 'simple'" class="description-simple">
        <div class="description-header">
          <span class="description-label">事件描述：</span>
          <a-space v-if="showActions">
            <a-tag v-if="wordCount > 0" color="blue" size="small">
              {{ wordCount }} 字
            </a-tag>
            
            <a-button 
              v-if="copyable" 
              type="text" 
              size="small"
              @click="copyDescription"
            >
              <CopyOutlined />
            </a-button>
          </a-space>
        </div>
        
        <div class="description-text" :class="{ 'description-collapsed': collapsible && !isExpanded }">
          <div v-if="highlightKeywords.length > 0" v-html="highlightedDescription"></div>
          <div v-else>{{ description }}</div>
        </div>
        
        <div v-if="collapsible && !isExpanded && description.length > maxLength" class="description-more">
          <a-button type="link" size="small" @click="toggleExpanded">
            查看更多...
          </a-button>
        </div>
      </div>

      <!-- 内联布局 -->
      <div v-else-if="layout === 'inline'" class="description-inline">
        <span class="description-label">描述：</span>
        <span class="description-text">
          <span v-if="highlightKeywords.length > 0" v-html="highlightedDescription"></span>
          <span v-else>{{ truncatedDescription }}</span>
        </span>
        
        <a-button 
          v-if="description.length > maxLength" 
          type="link" 
          size="small"
          @click="toggleExpanded"
        >
          {{ isExpanded ? '收起' : '展开' }}
        </a-button>
      </div>

      <!-- 富文本布局 -->
      <div v-else-if="layout === 'rich'" class="description-rich">
        <a-typography-paragraph
          :copyable="copyable"
          :ellipsis="collapsible ? { rows: ellipsisRows, expandable: true, symbol: '展开' } : false"
        >
          <template #copyableTooltip>复制事件描述</template>
          
          <div v-if="highlightKeywords.length > 0" v-html="highlightedDescription"></div>
          <div v-else>{{ description }}</div>
        </a-typography-paragraph>
      </div>
    </div>

    <!-- 关键词标签 -->
    <div v-if="showKeywords && extractedKeywords.length > 0" class="description-keywords">
      <div class="keywords-label">关键词：</div>
      <a-space wrap>
        <a-tag 
          v-for="keyword in extractedKeywords" 
          :key="keyword"
          color="processing"
          @click="highlightKeyword(keyword)"
        >
          {{ keyword }}
        </a-tag>
      </a-space>
    </div>

    <!-- 统计信息 -->
    <div v-if="showStatistics" class="description-statistics">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-statistic title="字数" :value="wordCount" />
        </a-col>
        <a-col :span="6">
          <a-statistic title="段落数" :value="paragraphCount" />
        </a-col>
        <a-col :span="6">
          <a-statistic title="关键词" :value="extractedKeywords.length" />
        </a-col>
        <a-col :span="6">
          <a-statistic title="阅读时间" :value="readingTime" suffix="分钟" :precision="1" />
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, defineProps, defineEmits } from 'vue'
import { message } from 'ant-design-vue'
import { 
  FileTextOutlined, 
  CopyOutlined, 
  UpOutlined, 
  DownOutlined 
} from '@ant-design/icons-vue'

// Props定义
interface Props {
  description?: string | null
  loading?: boolean
  error?: string | null
  layout?: 'card' | 'simple' | 'inline' | 'rich'
  size?: 'small' | 'middle' | 'large'
  bordered?: boolean
  collapsible?: boolean
  copyable?: boolean
  maxLength?: number
  ellipsisRows?: number
  showActions?: boolean
  showKeywords?: boolean
  showStatistics?: boolean
  highlightKeywords?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  error: null,
  layout: 'card',
  size: 'middle',
  bordered: true,
  collapsible: false,
  copyable: true,
  maxLength: 200,
  ellipsisRows: 3,
  showActions: true,
  showKeywords: false,
  showStatistics: false,
  highlightKeywords: () => []
})

// Emits定义
interface Emits {
  copy: [text: string]
  keywordClick: [keyword: string]
}

const emit = defineEmits<Emits>()

// 响应式数据
const isExpanded = ref(false)

// 计算属性
const componentClass = computed(() => ({
  [`event-description--${props.layout}`]: true,
  [`event-description--${props.size}`]: true,
  'event-description--bordered': props.bordered
}))

const wordCount = computed(() => {
  if (!props.description) return 0
  return props.description.replace(/\s/g, '').length
})

const paragraphCount = computed(() => {
  if (!props.description) return 0
  return props.description.split(/\n\s*\n/).filter(p => p.trim()).length
})

const readingTime = computed(() => {
  // 假设平均阅读速度为每分钟300字
  return wordCount.value / 300
})

const truncatedDescription = computed(() => {
  if (!props.description) return ''
  
  if (props.collapsible && !isExpanded.value && props.description.length > props.maxLength) {
    return props.description.substring(0, props.maxLength) + '...'
  }
  
  return props.description
})

const highlightedDescription = computed(() => {
  if (!props.description || props.highlightKeywords.length === 0) {
    return props.description
  }
  
  let highlighted = props.description
  
  props.highlightKeywords.forEach(keyword => {
    const regex = new RegExp(`(${keyword})`, 'gi')
    highlighted = highlighted.replace(regex, '<mark>$1</mark>')
  })
  
  return highlighted
})

const extractedKeywords = computed(() => {
  if (!props.description) return []
  
  // 简单的关键词提取逻辑
  const words = props.description
    .replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length >= 2)
  
  const wordCount: Record<string, number> = {}
  words.forEach(word => {
    wordCount[word] = (wordCount[word] || 0) + 1
  })
  
  return Object.entries(wordCount)
    .filter(([_, count]) => count >= 2)
    .sort(([_, a], [__, b]) => b - a)
    .slice(0, 10)
    .map(([word]) => word)
})

// 方法
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

const copyDescription = async () => {
  if (!props.description) return
  
  try {
    await navigator.clipboard.writeText(props.description)
    message.success('事件描述已复制到剪贴板')
    emit('copy', props.description)
  } catch (error) {
    message.error('复制失败')
  }
}

const highlightKeyword = (keyword: string) => {
  emit('keywordClick', keyword)
}
</script>

<style scoped>
/* 简化样式，避免SCSS语法问题 */
.event-description {
  .description-loading,
  .description-error,
  .description-empty {
    padding: 24px;
    text-align: center;
  }
  
  .description-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }
  
  .description-title {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .description-text {
    line-height: 1.6;
    color: #333;
  }

  .description-text.description-collapsed {
    max-height: 120px;
    overflow: hidden;
    position: relative;
  }

  .description-text.description-collapsed::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 30px;
    background: linear-gradient(transparent, white);
  }

  :deep(mark) {
    background-color: #fff3cd;
    padding: 2px 4px;
    border-radius: 2px;
  }
  
  .description-more {
    margin-top: 8px;
    text-align: center;
  }
  
  .description-simple {
    .description-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;
      
      .description-label {
        font-weight: 500;
        color: #333;
      }
    }
  }
  
  .description-inline {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    flex-wrap: wrap;
    
    .description-label {
      font-weight: 500;
      color: #333;
      flex-shrink: 0;
    }
    
    .description-text {
      flex: 1;
      min-width: 0;
    }
  }
  
  .description-keywords {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
    
    .keywords-label {
      margin-bottom: 8px;
      font-weight: 500;
      color: #666;
    }
  }
  
  .description-statistics {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }
}
</style>
