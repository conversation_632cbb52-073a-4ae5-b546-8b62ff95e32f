<template>
  <div class="reporter-info" :class="componentClass">
    <!-- 加载状态 -->
    <div v-if="loading" class="reporter-loading">
      <a-spin size="small" />
      <span>加载上报人信息...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="reporter-error">
      <a-alert type="error" :message="error" show-icon />
    </div>

    <!-- 空状态 -->
    <div v-else-if="!reporterData" class="reporter-empty">
      <a-empty description="暂无上报人信息" />
    </div>

    <!-- 正常显示 -->
    <div v-else class="reporter-content">
      <!-- 卡片布局 -->
      <a-card v-if="layout === 'card'" :bordered="bordered" size="small">
        <template #title>
          <div class="reporter-title">
            <UserOutlined />
            <span>上报人信息</span>
          </div>
        </template>
        
        <div class="reporter-card-content">
          <div class="reporter-avatar">
            <a-avatar :size="48" :src="reporterData.avatar">
              <template #icon><UserOutlined /></template>
            </a-avatar>
          </div>
          
          <div class="reporter-details">
            <div class="reporter-name">
              <span v-if="!sensitiveMode || !sensitiveFields.includes('reporterName')">
                {{ reporterData.name || '未知' }}
              </span>
              <span v-else class="sensitive-mask">***</span>
              <a-tag v-if="reporterData.role" :color="getRoleColor(reporterData.role)" size="small">
                {{ reporterData.role }}
              </a-tag>
            </div>
            
            <div class="reporter-department">
              <EnvironmentOutlined />
              <span>{{ reporterData.department || '未知部门' }}</span>
            </div>
            
            <div v-if="reporterData.phone" class="reporter-contact">
              <PhoneOutlined />
              <span v-if="!sensitiveMode || !sensitiveFields.includes('phone')">
                {{ reporterData.phone }}
              </span>
              <span v-else class="sensitive-mask">***-****-****</span>
            </div>
          </div>
        </div>
      </a-card>

      <!-- 表格布局 -->
      <a-descriptions v-else-if="layout === 'table'" :column="column" :bordered="bordered" :size="size">
        <a-descriptions-item label="上报人姓名">
          <span v-if="!sensitiveMode || !sensitiveFields.includes('reporterName')">
            {{ reporterData.name || '未知' }}
          </span>
          <span v-else class="sensitive-mask">***</span>
        </a-descriptions-item>
        
        <a-descriptions-item label="职务角色">
          <a-tag v-if="reporterData.role" :color="getRoleColor(reporterData.role)">
            {{ reporterData.role }}
          </a-tag>
          <span v-else>未知</span>
        </a-descriptions-item>
        
        <a-descriptions-item label="所属部门">
          {{ reporterData.department || '未知部门' }}
        </a-descriptions-item>
        
        <a-descriptions-item v-if="reporterData.phone" label="联系电话">
          <span v-if="!sensitiveMode || !sensitiveFields.includes('phone')">
            {{ reporterData.phone }}
          </span>
          <span v-else class="sensitive-mask">***-****-****</span>
        </a-descriptions-item>
        
        <a-descriptions-item v-if="reporterData.email" label="邮箱地址">
          <span v-if="!sensitiveMode || !sensitiveFields.includes('email')">
            {{ reporterData.email }}
          </span>
          <span v-else class="sensitive-mask">***@***.com</span>
        </a-descriptions-item>
      </a-descriptions>

      <!-- 内联布局 -->
      <div v-else-if="layout === 'inline'" class="reporter-inline">
        <span class="reporter-inline-item">
          <UserOutlined />
          <span v-if="!sensitiveMode || !sensitiveFields.includes('reporterName')">
            {{ reporterData.name || '未知' }}
          </span>
          <span v-else class="sensitive-mask">***</span>
        </span>
        
        <a-divider type="vertical" />
        
        <span class="reporter-inline-item">
          <EnvironmentOutlined />
          {{ reporterData.department || '未知部门' }}
        </span>
        
        <a-divider v-if="reporterData.role" type="vertical" />
        
        <a-tag v-if="reporterData.role" :color="getRoleColor(reporterData.role)" size="small">
          {{ reporterData.role }}
        </a-tag>
      </div>
    </div>

    <!-- 可点击字段处理 -->
    <div v-if="clickableFields.length > 0" class="reporter-actions">
      <a-space>
        <a-button 
          v-for="field in availableClickableFields" 
          :key="field"
          type="link" 
          size="small"
          @click="handleFieldClick(field, getFieldValue(field))"
        >
          查看{{ getFieldLabel(field) }}
        </a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits } from 'vue'
import { UserOutlined, EnvironmentOutlined, PhoneOutlined } from '@ant-design/icons-vue'
import type { ReporterData } from '../types'

// Props定义
interface Props {
  reporterData?: ReporterData | null
  loading?: boolean
  error?: string | null
  layout?: 'card' | 'table' | 'inline'
  size?: 'small' | 'middle' | 'large'
  bordered?: boolean
  column?: number
  sensitiveMode?: boolean
  sensitiveFields?: string[]
  clickableFields?: string[]
  showAvatar?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  error: null,
  layout: 'card',
  size: 'middle',
  bordered: true,
  column: 2,
  sensitiveMode: false,
  sensitiveFields: () => [],
  clickableFields: () => [],
  showAvatar: true
})

// Emits定义
interface Emits {
  fieldClick: [field: string, value: any]
}

const emit = defineEmits<Emits>()

// 计算属性
const componentClass = computed(() => ({
  [`reporter-info--${props.layout}`]: true,
  [`reporter-info--${props.size}`]: true,
  'reporter-info--bordered': props.bordered,
  'reporter-info--sensitive': props.sensitiveMode
}))

const availableClickableFields = computed(() => {
  if (!props.reporterData) return []
  
  return props.clickableFields.filter(field => {
    const value = getFieldValue(field)
    return value !== null && value !== undefined && value !== ''
  })
})

// 方法
const getRoleColor = (role: string): string => {
  const roleColors: Record<string, string> = {
    '医生': 'blue',
    '护士': 'green',
    '主任': 'purple',
    '护士长': 'orange',
    '技师': 'cyan',
    '药师': 'magenta'
  }
  return roleColors[role] || 'default'
}

const getFieldValue = (field: string): any => {
  if (!props.reporterData) return null
  
  const fieldMap: Record<string, keyof ReporterData> = {
    'reporterName': 'name',
    'department': 'department',
    'role': 'role',
    'phone': 'phone',
    'email': 'email'
  }
  
  const mappedField = fieldMap[field]
  return mappedField ? props.reporterData[mappedField] : null
}

const getFieldLabel = (field: string): string => {
  const labelMap: Record<string, string> = {
    'reporterName': '上报人',
    'department': '部门',
    'role': '角色',
    'phone': '电话',
    'email': '邮箱'
  }
  return labelMap[field] || field
}

const handleFieldClick = (field: string, value: any) => {
  emit('fieldClick', field, value)
}
</script>

<style scoped>
/* 简化样式，避免SCSS语法问题 */
.reporter-info {
  &--card {
    .reporter-card-content {
      display: flex;
      gap: 16px;
      align-items: flex-start;
    }
    
    .reporter-avatar {
      flex-shrink: 0;
    }
    
    .reporter-details {
      flex: 1;
      
      .reporter-name {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        gap: 8px;
      }
      
      .reporter-department,
      .reporter-contact {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #666;
        margin-bottom: 4px;
        font-size: 14px;
      }
    }
  }
  
  &--inline {
    .reporter-inline {
      display: flex;
      align-items: center;
      gap: 8px;
      flex-wrap: wrap;
      
      .reporter-inline-item {
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }
  }
  
  .reporter-title {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .reporter-loading,
  .reporter-error,
  .reporter-empty {
    padding: 16px;
    text-align: center;
  }
  
  .reporter-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }
  
  .sensitive-mask {
    color: #999;
    font-style: italic;
  }
  
  .reporter-actions {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #f0f0f0;
  }
}
</style>
