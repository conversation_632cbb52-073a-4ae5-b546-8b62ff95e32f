<template>
  <div class="event-timeline" :class="componentClass">
    <!-- 加载状态 -->
    <div v-if="loading" class="timeline-loading">
      <a-spin size="small" />
      <span>加载时间线...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="timeline-error">
      <a-alert type="error" :message="error" show-icon />
    </div>

    <!-- 空状态 -->
    <div v-else-if="!timelineData || timelineData.length === 0" class="timeline-empty">
      <a-empty description="暂无时间线数据" />
    </div>

    <!-- 正常显示 -->
    <div v-else class="timeline-content">
      <!-- 时间线布局 -->
      <a-timeline v-if="layout === 'timeline'" :mode="timelineMode">
        <a-timeline-item
          v-for="(item, index) in sortedTimelineData"
          :key="index"
          :color="getTimelineColor(item.type)"
          :dot="getTimelineDot(item.type)"
        >
          <template #label v-if="timelineMode === 'left' || timelineMode === 'right'">
            <div class="timeline-label">
              <div class="timeline-time">{{ formatTime(item.time) }}</div>
              <div v-if="item.duration" class="timeline-duration">
                耗时: {{ item.duration }}
              </div>
            </div>
          </template>
          
          <div class="timeline-item-content">
            <div class="timeline-header">
              <span class="timeline-title">{{ item.title }}</span>
              <a-tag :color="getStatusColor(item.status)" size="small">
                {{ item.status }}
              </a-tag>
            </div>
            
            <div v-if="item.description" class="timeline-description">
              {{ item.description }}
            </div>
            
            <div v-if="item.operator" class="timeline-operator">
              <UserOutlined />
              <span>{{ item.operator }}</span>
            </div>
            
            <div v-if="timelineMode === 'alternate'" class="timeline-time-alternate">
              {{ formatTime(item.time) }}
            </div>
          </div>
        </a-timeline-item>
      </a-timeline>

      <!-- 表格布局 -->
      <a-table 
        v-else-if="layout === 'table'"
        :dataSource="sortedTimelineData"
        :columns="tableColumns"
        :pagination="false"
        :size="size"
        :bordered="bordered"
        row-key="time"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'time'">
            <div class="table-time">
              <ClockCircleOutlined />
              <span>{{ formatTime(record.time) }}</span>
            </div>
          </template>
          
          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ record.status }}
            </a-tag>
          </template>
          
          <template v-else-if="column.key === 'type'">
            <a-tag :color="getTimelineColor(record.type)">
              {{ getTypeLabel(record.type) }}
            </a-tag>
          </template>
          
          <template v-else-if="column.key === 'operator'">
            <div v-if="record.operator" class="table-operator">
              <UserOutlined />
              <span>{{ record.operator }}</span>
            </div>
            <span v-else>-</span>
          </template>
        </template>
      </a-table>

      <!-- 卡片布局 -->
      <div v-else-if="layout === 'cards'" class="timeline-cards">
        <a-card
          v-for="(item, index) in sortedTimelineData"
          :key="index"
          :size="size"
          :bordered="bordered"
          class="timeline-card"
        >
          <template #title>
            <div class="card-title">
              <component :is="getTimelineDot(item.type)" />
              <span>{{ item.title }}</span>
              <a-tag :color="getStatusColor(item.status)" size="small">
                {{ item.status }}
              </a-tag>
            </div>
          </template>
          
          <template #extra>
            <div class="card-time">
              {{ formatTime(item.time) }}
            </div>
          </template>
          
          <div class="card-content">
            <div v-if="item.description" class="card-description">
              {{ item.description }}
            </div>
            
            <div v-if="item.operator" class="card-operator">
              <UserOutlined />
              <span>操作人: {{ item.operator }}</span>
            </div>
            
            <div v-if="item.duration" class="card-duration">
              <ClockCircleOutlined />
              <span>耗时: {{ item.duration }}</span>
            </div>
          </div>
        </a-card>
      </div>
    </div>

    <!-- 统计信息 -->
    <div v-if="showStatistics && timelineData && timelineData.length > 0" class="timeline-statistics">
      <a-statistic-group>
        <a-statistic title="总事件数" :value="timelineData.length" />
        <a-statistic title="总耗时" :value="totalDuration" suffix="小时" />
        <a-statistic title="平均耗时" :value="averageDuration" suffix="小时" :precision="1" />
      </a-statistic-group>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps } from 'vue'
import { 
  UserOutlined, 
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SyncOutlined,
  CloseCircleOutlined
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'
import type { TimelineData } from '../types'

// Props定义
interface Props {
  timelineData?: TimelineData[] | null
  loading?: boolean
  error?: string | null
  layout?: 'timeline' | 'table' | 'cards'
  timelineMode?: 'left' | 'right' | 'alternate'
  size?: 'small' | 'middle' | 'large'
  bordered?: boolean
  showStatistics?: boolean
  sortOrder?: 'asc' | 'desc'
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  error: null,
  layout: 'timeline',
  timelineMode: 'left',
  size: 'middle',
  bordered: true,
  showStatistics: false,
  sortOrder: 'asc'
})

// 计算属性
const componentClass = computed(() => ({
  [`event-timeline--${props.layout}`]: true,
  [`event-timeline--${props.size}`]: true,
  'event-timeline--bordered': props.bordered
}))

const sortedTimelineData = computed(() => {
  if (!props.timelineData) return []
  
  const sorted = [...props.timelineData].sort((a, b) => {
    const timeA = new Date(a.time).getTime()
    const timeB = new Date(b.time).getTime()
    return props.sortOrder === 'asc' ? timeA - timeB : timeB - timeA
  })
  
  return sorted
})

const tableColumns = computed(() => [
  {
    title: '时间',
    key: 'time',
    dataIndex: 'time',
    width: 180
  },
  {
    title: '事件',
    key: 'title',
    dataIndex: 'title'
  },
  {
    title: '类型',
    key: 'type',
    dataIndex: 'type',
    width: 100
  },
  {
    title: '状态',
    key: 'status',
    dataIndex: 'status',
    width: 100
  },
  {
    title: '操作人',
    key: 'operator',
    dataIndex: 'operator',
    width: 120
  },
  {
    title: '描述',
    key: 'description',
    dataIndex: 'description'
  }
])

const totalDuration = computed(() => {
  if (!props.timelineData) return 0
  
  return props.timelineData.reduce((total, item) => {
    if (item.duration) {
      const hours = parseFloat(item.duration.replace(/[^\d.]/g, ''))
      return total + (isNaN(hours) ? 0 : hours)
    }
    return total
  }, 0)
})

const averageDuration = computed(() => {
  if (!props.timelineData || props.timelineData.length === 0) return 0
  return totalDuration.value / props.timelineData.length
})

// 方法
const formatTime = (time: string | Date): string => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

const getTimelineColor = (type: string): string => {
  const colorMap: Record<string, string> = {
    'create': 'blue',
    'update': 'orange',
    'complete': 'green',
    'error': 'red',
    'warning': 'yellow',
    'info': 'cyan'
  }
  return colorMap[type] || 'blue'
}

const getStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    '已完成': 'green',
    '进行中': 'blue',
    '待处理': 'orange',
    '已取消': 'red',
    '暂停': 'yellow'
  }
  return colorMap[status] || 'default'
}

const getTimelineDot = (type: string) => {
  const dotMap: Record<string, any> = {
    'create': CheckCircleOutlined,
    'update': SyncOutlined,
    'complete': CheckCircleOutlined,
    'error': CloseCircleOutlined,
    'warning': ExclamationCircleOutlined,
    'info': ClockCircleOutlined
  }
  return dotMap[type] || ClockCircleOutlined
}

const getTypeLabel = (type: string): string => {
  const labelMap: Record<string, string> = {
    'create': '创建',
    'update': '更新',
    'complete': '完成',
    'error': '错误',
    'warning': '警告',
    'info': '信息'
  }
  return labelMap[type] || type
}
</script>

<style scoped>
/* 简化样式，避免SCSS语法问题 */
  .timeline-loading,
  .timeline-error,
  .timeline-empty {
    padding: 24px;
    text-align: center;
  }
  
  .timeline-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }
  
  .timeline-label {
    text-align: right;
  }

  .timeline-label .timeline-time {
    font-weight: 500;
    color: #1890ff;
  }

  .timeline-label .timeline-duration {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
  }
  
  .timeline-item-content {
    .timeline-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
      
      .timeline-title {
        font-weight: 500;
      }
    }
    
    .timeline-description {
      color: #666;
      margin-bottom: 8px;
      line-height: 1.5;
    }
    
    .timeline-operator {
      display: flex;
      align-items: center;
      gap: 4px;
      color: #999;
      font-size: 12px;
    }
    
    .timeline-time-alternate {
      margin-top: 8px;
      color: #1890ff;
      font-size: 12px;
    }
  }
  
  .table-time,
  .table-operator {
    display: flex;
    align-items: center;
    gap: 4px;
  }
  
  .timeline-cards {
    display: flex;
    flex-direction: column;
    gap: 16px;
    
    .timeline-card {
      .card-title {
        display: flex;
        align-items: center;
        gap: 8px;
      }
      
      .card-time {
        color: #1890ff;
        font-size: 12px;
      }
      
      .card-content {
        .card-description {
          margin-bottom: 12px;
          line-height: 1.5;
        }
        
        .card-operator,
        .card-duration {
          display: flex;
          align-items: center;
          gap: 4px;
          color: #666;
          font-size: 12px;
          margin-bottom: 4px;
        }
      }
    }
  }
  
  .timeline-statistics {
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }
</style>
