import type { AppRouteModule } from '@/router/types'
import { LAYOUT } from '@/router/constant'

const test: AppRouteModule = {
  path: '/test',
  name: 'Test',
  component: LAYOUT,
  redirect: '/test/event-info',
  meta: {
    orderNo: 9999,
    icon: 'ion:bug-outline',
    title: '组件测试',
    hideChildrenInMenu: false,
  },
  children: [
    {
      path: 'event-info',
      name: 'EventInfoTest',
      component: () => import('@/views/test/EventInfoTest.vue'),
      meta: {
        title: 'EventInfo组件测试',
        icon: 'ion:information-circle-outline',
      },
    },
  ],
}

export default test
