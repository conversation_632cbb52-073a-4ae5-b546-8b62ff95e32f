<template>
  <div class="dynamic-fields" :class="componentClass">
    <!-- 加载状态 -->
    <div v-if="loading" class="fields-loading">
      <a-spin size="small" />
      <span>加载动态字段...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="fields-error">
      <a-alert type="error" :message="error" show-icon />
    </div>

    <!-- 空状态 -->
    <div v-else-if="!fieldsData || Object.keys(fieldsData).length === 0" class="fields-empty">
      <a-empty description="暂无动态字段数据" />
    </div>

    <!-- 正常显示 -->
    <div v-else class="fields-content">
      <!-- 卡片布局 -->
      <a-card v-if="layout === 'card'" :bordered="bordered" :size="size">
        <template #title>
          <div class="fields-title">
            <SettingOutlined />
            <span>动态字段</span>
            <a-tag color="blue" size="small">
              {{ Object.keys(fieldsData).length }} 个字段
            </a-tag>
          </div>
        </template>
        
        <template #extra v-if="showActions">
          <a-space>
            <a-button 
              v-if="collapsible" 
              type="text" 
              size="small"
              @click="toggleExpanded"
            >
              {{ isExpanded ? '收起' : '展开' }}
              <component :is="isExpanded ? 'UpOutlined' : 'DownOutlined'" />
            </a-button>
            
            <a-dropdown>
              <template #overlay>
                <a-menu @click="handleMenuClick">
                  <a-menu-item key="export">
                    <ExportOutlined />
                    导出字段
                  </a-menu-item>
                  <a-menu-item key="copy">
                    <CopyOutlined />
                    复制数据
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button type="text" size="small">
                <MoreOutlined />
              </a-button>
            </a-dropdown>
          </a-space>
        </template>
        
        <div class="fields-grid" :class="{ 'fields-collapsed': collapsible && !isExpanded }">
          <div 
            v-for="(value, key) in visibleFields" 
            :key="key"
            class="field-item"
            :class="getFieldClass(key, value)"
          >
            <div class="field-label">
              <component :is="getFieldIcon(key)" />
              <span>{{ getFieldLabel(key) }}</span>
              <a-tag v-if="getFieldType(value)" :color="getTypeColor(getFieldType(value))" size="small">
                {{ getFieldType(value) }}
              </a-tag>
            </div>
            
            <div class="field-value">
              <!-- 文本类型 -->
              <span v-if="isTextType(value)" class="field-text">
                {{ formatFieldValue(value) }}
              </span>
              
              <!-- 数字类型 -->
              <a-statistic 
                v-else-if="isNumberType(value)" 
                :value="value" 
                :precision="getNumberPrecision(value)"
                class="field-number"
              />
              
              <!-- 布尔类型 -->
              <a-tag 
                v-else-if="isBooleanType(value)" 
                :color="value ? 'green' : 'red'"
                class="field-boolean"
              >
                {{ value ? '是' : '否' }}
              </a-tag>
              
              <!-- 日期类型 -->
              <div v-else-if="isDateType(value)" class="field-date">
                <CalendarOutlined />
                <span>{{ formatDate(value) }}</span>
              </div>
              
              <!-- 数组类型 -->
              <div v-else-if="isArrayType(value)" class="field-array">
                <a-tag 
                  v-for="(item, index) in value" 
                  :key="index"
                  color="processing"
                >
                  {{ item }}
                </a-tag>
              </div>
              
              <!-- 对象类型 -->
              <div v-else-if="isObjectType(value)" class="field-object">
                <a-descriptions :column="1" size="small">
                  <a-descriptions-item 
                    v-for="(objValue, objKey) in value" 
                    :key="objKey"
                    :label="objKey"
                  >
                    {{ objValue }}
                  </a-descriptions-item>
                </a-descriptions>
              </div>
              
              <!-- 其他类型 -->
              <span v-else class="field-other">
                {{ String(value) }}
              </span>
            </div>
          </div>
        </div>
      </a-card>

      <!-- 表格布局 -->
      <a-table 
        v-else-if="layout === 'table'"
        :dataSource="tableData"
        :columns="tableColumns"
        :pagination="false"
        :size="size"
        :bordered="bordered"
        row-key="key"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'value'">
            <div class="table-value">
              <component :is="renderFieldValue(record.value)" />
            </div>
          </template>
          
          <template v-else-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.type)">
              {{ record.type }}
            </a-tag>
          </template>
        </template>
      </a-table>

      <!-- 描述列表布局 -->
      <a-descriptions 
        v-else-if="layout === 'descriptions'"
        :column="column"
        :bordered="bordered"
        :size="size"
      >
        <a-descriptions-item 
          v-for="(value, key) in fieldsData" 
          :key="key"
          :label="getFieldLabel(key)"
        >
          <div class="description-value">
            {{ formatFieldValue(value) }}
          </div>
        </a-descriptions-item>
      </a-descriptions>
    </div>

    <!-- 字段统计 -->
    <div v-if="showStatistics" class="fields-statistics">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-statistic title="总字段数" :value="totalFields" />
        </a-col>
        <a-col :span="6">
          <a-statistic title="有值字段" :value="fieldsWithValue" />
        </a-col>
        <a-col :span="6">
          <a-statistic title="空字段" :value="emptyFields" />
        </a-col>
        <a-col :span="6">
          <a-statistic title="完整度" :value="completeness" suffix="%" :precision="1" />
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, defineProps, defineEmits } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { 
  SettingOutlined,
  UpOutlined,
  DownOutlined,
  ExportOutlined,
  CopyOutlined,
  MoreOutlined,
  CalendarOutlined,
  FileTextOutlined,
  NumberOutlined,
  CheckCircleOutlined
} from '@ant-design/icons-vue'

// Props定义
interface Props {
  fieldsData?: Record<string, any> | null
  loading?: boolean
  error?: string | null
  layout?: 'card' | 'table' | 'descriptions'
  size?: 'small' | 'middle' | 'large'
  bordered?: boolean
  column?: number
  collapsible?: boolean
  showActions?: boolean
  showStatistics?: boolean
  maxVisibleFields?: number
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  error: null,
  layout: 'card',
  size: 'middle',
  bordered: true,
  column: 2,
  collapsible: false,
  showActions: true,
  showStatistics: false,
  maxVisibleFields: 10
})

// Emits定义
interface Emits {
  export: [data: Record<string, any>]
  copy: [data: Record<string, any>]
}

const emit = defineEmits<Emits>()

// 响应式数据
const isExpanded = ref(false)

// 计算属性
const componentClass = computed(() => ({
  [`dynamic-fields--${props.layout}`]: true,
  [`dynamic-fields--${props.size}`]: true,
  'dynamic-fields--bordered': props.bordered
}))

const visibleFields = computed(() => {
  if (!props.fieldsData) return {}
  
  const entries = Object.entries(props.fieldsData)
  
  if (props.collapsible && !isExpanded.value) {
    return Object.fromEntries(entries.slice(0, props.maxVisibleFields))
  }
  
  return props.fieldsData
})

const tableData = computed(() => {
  if (!props.fieldsData) return []
  
  return Object.entries(props.fieldsData).map(([key, value]) => ({
    key,
    label: getFieldLabel(key),
    value,
    type: getFieldType(value)
  }))
})

const tableColumns = computed(() => [
  {
    title: '字段名',
    key: 'label',
    dataIndex: 'label',
    width: 200
  },
  {
    title: '类型',
    key: 'type',
    dataIndex: 'type',
    width: 100
  },
  {
    title: '值',
    key: 'value',
    dataIndex: 'value'
  }
])

const totalFields = computed(() => {
  return props.fieldsData ? Object.keys(props.fieldsData).length : 0
})

const fieldsWithValue = computed(() => {
  if (!props.fieldsData) return 0
  
  return Object.values(props.fieldsData).filter(value => 
    value !== null && value !== undefined && value !== ''
  ).length
})

const emptyFields = computed(() => {
  return totalFields.value - fieldsWithValue.value
})

const completeness = computed(() => {
  if (totalFields.value === 0) return 0
  return (fieldsWithValue.value / totalFields.value) * 100
})

// 方法
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

const handleMenuClick = ({ key }: { key: string }) => {
  switch (key) {
    case 'export':
      handleExport()
      break
    case 'copy':
      handleCopy()
      break
  }
}

const handleExport = () => {
  if (!props.fieldsData) return
  
  emit('export', props.fieldsData)
  message.success('字段数据导出成功')
}

const handleCopy = async () => {
  if (!props.fieldsData) return
  
  try {
    const text = JSON.stringify(props.fieldsData, null, 2)
    await navigator.clipboard.writeText(text)
    emit('copy', props.fieldsData)
    message.success('字段数据已复制到剪贴板')
  } catch (error) {
    message.error('复制失败')
  }
}

const getFieldLabel = (key: string): string => {
  // 字段名称映射
  const labelMap: Record<string, string> = {
    'patientAge': '患者年龄',
    'patientGender': '患者性别',
    'eventLocation': '事件地点',
    'eventSeverity': '严重程度',
    'reportTime': '上报时间',
    'eventTime': '事件时间'
  }
  
  return labelMap[key] || key.replace(/([A-Z])/g, ' $1').trim()
}

const getFieldType = (value: any): string => {
  if (value === null || value === undefined) return 'null'
  if (typeof value === 'boolean') return 'boolean'
  if (typeof value === 'number') return 'number'
  if (typeof value === 'string') {
    if (isDateString(value)) return 'date'
    return 'string'
  }
  if (Array.isArray(value)) return 'array'
  if (typeof value === 'object') return 'object'
  return 'unknown'
}

const getFieldClass = (key: string, value: any): string => {
  const type = getFieldType(value)
  return `field-item--${type}`
}

const getFieldIcon = (key: string) => {
  const iconMap: Record<string, any> = {
    'string': FileTextOutlined,
    'number': NumberOutlined,
    'boolean': CheckCircleOutlined,
    'date': CalendarOutlined
  }
  
  const type = getFieldType(props.fieldsData?.[key])
  return iconMap[type] || FileTextOutlined
}

const getTypeColor = (type: string): string => {
  const colorMap: Record<string, string> = {
    'string': 'blue',
    'number': 'green',
    'boolean': 'orange',
    'date': 'purple',
    'array': 'cyan',
    'object': 'magenta',
    'null': 'default'
  }
  return colorMap[type] || 'default'
}

const isTextType = (value: any): boolean => {
  return typeof value === 'string' && !isDateString(value)
}

const isNumberType = (value: any): boolean => {
  return typeof value === 'number'
}

const isBooleanType = (value: any): boolean => {
  return typeof value === 'boolean'
}

const isDateType = (value: any): boolean => {
  return typeof value === 'string' && isDateString(value)
}

const isArrayType = (value: any): boolean => {
  return Array.isArray(value)
}

const isObjectType = (value: any): boolean => {
  return typeof value === 'object' && value !== null && !Array.isArray(value)
}

const isDateString = (value: string): boolean => {
  return dayjs(value).isValid() && /\d{4}-\d{2}-\d{2}/.test(value)
}

const formatFieldValue = (value: any): string => {
  if (value === null || value === undefined) return '-'
  if (typeof value === 'boolean') return value ? '是' : '否'
  if (typeof value === 'object') return JSON.stringify(value)
  return String(value)
}

const formatDate = (value: string): string => {
  return dayjs(value).format('YYYY-MM-DD HH:mm:ss')
}

const getNumberPrecision = (value: number): number => {
  return value % 1 === 0 ? 0 : 2
}

const renderFieldValue = (value: any) => {
  // 返回渲染函数或组件
  return () => formatFieldValue(value)
}
</script>

<style scoped>
/* 简化样式，避免SCSS语法问题 */
.dynamic-fields {
  .fields-loading,
  .fields-error,
  .fields-empty {
    padding: 24px;
    text-align: center;
  }
  
  .fields-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }
  
  .fields-title {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .fields-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;
  }

  .fields-grid.fields-collapsed {
    max-height: 400px;
    overflow: hidden;
    
    .field-item {
      padding: 12px;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      background: #fafafa;
      
      .field-label {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
        font-weight: 500;
        color: #333;
      }
      
      .field-value {
        .field-text {
          word-break: break-all;
        }
        
        .field-date {
          display: flex;
          align-items: center;
          gap: 4px;
        }
        
        .field-array {
          display: flex;
          flex-wrap: wrap;
          gap: 4px;
        }
        
        .field-object {
          background: white;
          padding: 8px;
          border-radius: 4px;
        }
      }
    }
  }
  
  .table-value {
    word-break: break-all;
  }
  
  .description-value {
    word-break: break-all;
  }
  
  .fields-statistics {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }
}
</style>
